// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Company Settings
model CompanySettings {
  id        String   @id @default(cuid())
  name      String
  nameEn    String?
  logo      String?
  address   String?
  addressEn String?
  phone     String?
  email     String?
  website   String?
  currency  String   @default("LYD") // LYD, EGP, SAR
  language  String   @default("ar") // ar, en
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("company_settings")
}

// Chart of Accounts
model Account {
  id          String      @id @default(cuid())
  code        String      @unique
  name        String
  nameEn      String?
  type        AccountType
  parentId    String?
  parent      Account?    @relation("AccountHierarchy", fields: [parentId], references: [id])
  children    Account[]   @relation("AccountHierarchy")
  level       Int         @default(1)
  isActive    Boolean     @default(true)
  balance     Decimal     @default(0)
  debitTotal  Decimal     @default(0)
  creditTotal Decimal     @default(0)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  debitEntries  JournalEntry[] @relation("DebitAccount")
  creditEntries JournalEntry[] @relation("CreditAccount")

  @@map("accounts")
}

enum AccountType {
  ASSET
  LIABILITY
  EQUITY
  REVENUE
  EXPENSE
}

// Journal Entries
model Journal {
  id          String      @id @default(cuid())
  number      String      @unique
  date        DateTime
  description String
  reference   String?
  type        JournalType @default(MANUAL)
  sourceId    String? // Reference to source transaction (sales, purchases, etc.)
  sourceType  String? // Type of source (SALES, PURCHASES, INVENTORY, etc.)
  totalDebit  Decimal
  totalCredit Decimal
  isPosted    Boolean     @default(false)
  createdBy   String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  entries JournalEntry[]

  @@map("journals")
}

enum JournalType {
  MANUAL
  AUTOMATIC
  SALES
  PURCHASES
  INVENTORY
  PAYROLL
  PAYMENTS
  RECEIPTS
}

model JournalEntry {
  id              String   @id @default(cuid())
  journalId       String
  journal         Journal  @relation(fields: [journalId], references: [id], onDelete: Cascade)
  accountId       String
  account         Account  @relation("DebitAccount", fields: [accountId], references: [id])
  creditAccountId String?
  creditAccount   Account? @relation("CreditAccount", fields: [creditAccountId], references: [id])
  description     String
  debit           Decimal  @default(0)
  credit          Decimal  @default(0)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("journal_entries")
}

// Financial Periods
model FinancialPeriod {
  id        String   @id @default(cuid())
  name      String
  startDate DateTime
  endDate   DateTime
  isActive  Boolean  @default(false)
  isClosed  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("financial_periods")
}

// Inventory Items (for future use)
model InventoryItem {
  id          String   @id @default(cuid())
  code        String   @unique
  name        String
  nameEn      String?
  description String?
  unit        String
  costPrice   Decimal  @default(0)
  salePrice   Decimal  @default(0)
  quantity    Decimal  @default(0)
  minQuantity Decimal  @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("inventory_items")
}

// Customers (for future use)
model Customer {
  id        String   @id @default(cuid())
  code      String   @unique
  name      String
  nameEn    String?
  phone     String?
  email     String?
  address   String?
  balance   Decimal  @default(0)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("customers")
}

// Suppliers (for future use)
model Supplier {
  id        String   @id @default(cuid())
  code      String   @unique
  name      String
  nameEn    String?
  phone     String?
  email     String?
  address   String?
  balance   Decimal  @default(0)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("suppliers")
}

// Employees (for future use)
model Employee {
  id         String   @id @default(cuid())
  code       String   @unique
  name       String
  nameEn     String?
  position   String?
  department String?
  salary     Decimal  @default(0)
  phone      String?
  email      String?
  address    String?
  hireDate   DateTime
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("employees")
}
