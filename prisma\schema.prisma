// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Company Settings
model CompanySettings {
  id          String   @id @default(cuid())
  name        String
  nameEn      String?
  logo        String?
  address     String?
  addressEn   String?
  phone       String?
  email       String?
  website     String?
  currency    String   @default("LYD") // LYD, EGP, SAR
  language    String   @default("ar")  // ar, en
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("company_settings")
}

// Chart of Accounts
model Account {
  id          String    @id @default(cuid())
  code        String    @unique
  name        String
  nameEn      String?
  type        AccountType
  parentId    String?
  parent      Account?  @relation("AccountHierarchy", fields: [parentId], references: [id])
  children    Account[] @relation("AccountHierarchy")
  level       Int       @default(1)
  isActive    Boolean   @default(true)
  balance     Decimal   @default(0)
  debitTotal  Decimal   @default(0)
  creditTotal Decimal   @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  debitEntries  JournalEntry[] @relation("DebitAccount")
  creditEntries JournalEntry[] @relation("CreditAccount")
  
  // Sales Journal Settings Relations
  salesDebitSettings    SalesJournalSettings[] @relation("SalesDebitAccount")
  salesCreditSettings   SalesJournalSettings[] @relation("SalesCreditAccount")
  returnDebitSettings   SalesJournalSettings[] @relation("ReturnDebitAccount")
  returnCreditSettings  SalesJournalSettings[] @relation("ReturnCreditAccount")
  paymentDebitSettings  SalesJournalSettings[] @relation("PaymentDebitAccount")
  paymentCreditSettings SalesJournalSettings[] @relation("PaymentCreditAccount")

  @@map("accounts")
}

enum AccountType {
  ASSET
  LIABILITY
  EQUITY
  REVENUE
  EXPENSE
}

// Journal Entries
model Journal {
  id          String         @id @default(cuid())
  number      String         @unique
  date        DateTime
  description String
  reference   String?
  type        JournalType    @default(MANUAL)
  sourceId    String?        // Reference to source transaction (sales, purchases, etc.)
  sourceType  String?        // Type of source (SALES, PURCHASES, INVENTORY, etc.)
  totalDebit  Decimal
  totalCredit Decimal
  isPosted    Boolean        @default(false)
  createdBy   String?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Relations
  entries     JournalEntry[]
  salesInvoice SalesInvoice?
  salesPayment SalesPayment?

  @@map("journals")
}

enum JournalType {
  MANUAL
  AUTOMATIC
  SALES
  PURCHASES
  INVENTORY
  PAYROLL
  PAYMENTS
  RECEIPTS
}

model JournalEntry {
  id          String   @id @default(cuid())
  journalId   String
  journal     Journal  @relation(fields: [journalId], references: [id], onDelete: Cascade)
  accountId   String
  account     Account  @relation("DebitAccount", fields: [accountId], references: [id])
  creditAccountId String?
  creditAccount   Account? @relation("CreditAccount", fields: [creditAccountId], references: [id])
  description String
  debit       Decimal  @default(0)
  credit      Decimal  @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("journal_entries")
}

// Financial Periods
model FinancialPeriod {
  id        String   @id @default(cuid())
  name      String
  startDate DateTime
  endDate   DateTime
  isActive  Boolean  @default(false)
  isClosed  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("financial_periods")
}

// Inventory Items (for future use)
model InventoryItem {
  id          String   @id @default(cuid())
  code        String   @unique
  name        String
  nameEn      String?
  description String?
  unit        String
  costPrice   Decimal  @default(0)
  salePrice   Decimal  @default(0)
  quantity    Decimal  @default(0)
  minQuantity Decimal  @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("inventory_items")
}

// Customers (for future use)
model Customer {
  id          String   @id @default(cuid())
  code        String   @unique
  name        String
  nameEn      String?
  phone       String?
  email       String?
  address     String?
  taxNumber   String?  // الرقم الضريبي
  balance     Decimal  @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  invoices    SalesInvoice[]

  @@map("customers")
}

// Suppliers (for future use)
model Supplier {
  id        String   @id @default(cuid())
  code      String   @unique
  name      String
  nameEn    String?
  phone     String?
  email     String?
  address   String?
  balance   Decimal  @default(0)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("suppliers")
}

// Employees (for future use)
model Employee {
  id         String   @id @default(cuid())
  code       String   @unique
  name       String
  nameEn     String?
  position   String?
  department String?
  salary     Decimal  @default(0)
  phone      String?
  email      String?
  address    String?
  hireDate   DateTime
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("employees")
}

// Sales Module
model SalesInvoice {
  id              String           @id @default(cuid())
  number          String           @unique // S-001, S-002, etc.
  date            DateTime
  customerId      String
  customer        Customer         @relation(fields: [customerId], references: [id])
  status          InvoiceStatus    @default(DRAFT)
  subtotal        Decimal          @default(0)
  discount        Decimal          @default(0)
  tax             Decimal          @default(0)
  total           Decimal          @default(0)
  paid            Decimal          @default(0)
  remaining       Decimal          @default(0)
  notes           String?
  isPosted        Boolean          @default(false)
  postedAt        DateTime?
  postedBy        String?
  createdBy       String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  // Relations
  items           SalesInvoiceItem[]
  services        SalesInvoiceService[]
  payments        SalesPayment[]
  journal         Journal?         @relation(fields: [journalId], references: [id])
  journalId       String?          @unique

  @@map("sales_invoices")
}

enum InvoiceStatus {
  DRAFT      // مبدئية
  FINAL      // نهائية
  CANCELLED  // ملغاة
}

model SalesInvoiceItem {
  id              String        @id @default(cuid())
  invoiceId       String
  invoice         SalesInvoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  itemCode        String        // كود الصنف
  itemName        String        // اسم الصنف
  length          Decimal       @default(0) // الطول
  width           Decimal       @default(0) // العرض
  quantity        Decimal       @default(0) // الكمية
  squareMeters    Decimal       @default(0) // المتر المربع
  unitPrice       Decimal       @default(0) // سعر المتر
  total           Decimal       @default(0) // الإجمالي
  notes           String?       // ملاحظات
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@map("sales_invoice_items")
}

model SalesService {
  id              String        @id @default(cuid())
  name            String        // اسم الخدمة
  nameEn          String?
  price           Decimal       @default(0)
  isActive        Boolean       @default(true)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  invoiceServices SalesInvoiceService[]

  @@map("sales_services")
}

model SalesInvoiceService {
  id              String        @id @default(cuid())
  invoiceId       String
  invoice         SalesInvoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  serviceId       String
  service         SalesService  @relation(fields: [serviceId], references: [id])
  quantity        Decimal       @default(1)
  price           Decimal       @default(0)
  total           Decimal       @default(0)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@map("sales_invoice_services")
}

model SalesPayment {
  id              String        @id @default(cuid())
  invoiceId       String
  invoice         SalesInvoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  amount          Decimal
  paymentMethod   PaymentMethod @default(CASH)
  reference       String?       // رقم الشيك أو التحويل
  notes           String?
  isPosted        Boolean       @default(false)
  postedAt        DateTime?
  postedBy        String?
  createdBy       String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  journal         Journal?      @relation(fields: [journalId], references: [id])
  journalId       String?       @unique

  @@map("sales_payments")
}

enum PaymentMethod {
  CASH       // نقداً
  CHECK      // شيك
  TRANSFER   // تحويل بنكي
  CREDIT     // آجل
}

// Sales Journal Settings
model SalesJournalSettings {
  id                    String   @id @default(cuid())
  salesDebitAccountId   String   // حساب مدين (العملاء)
  salesDebitAccount     Account  @relation("SalesDebitAccount", fields: [salesDebitAccountId], references: [id])
  salesCreditAccountId  String   // حساب دائن (المبيعات)
  salesCreditAccount    Account  @relation("SalesCreditAccount", fields: [salesCreditAccountId], references: [id])
  returnDebitAccountId  String   // حساب مدين (مردودات المبيعات)
  returnDebitAccount    Account  @relation("ReturnDebitAccount", fields: [returnDebitAccountId], references: [id])
  returnCreditAccountId String   // حساب دائن (العملاء - مردودات)
  returnCreditAccount   Account  @relation("ReturnCreditAccount", fields: [returnCreditAccountId], references: [id])
  paymentDebitAccountId String   // حساب مدين (النقدية/البنك)
  paymentDebitAccount   Account  @relation("PaymentDebitAccount", fields: [paymentDebitAccountId], references: [id])
  paymentCreditAccountId String  // حساب دائن (العملاء - سداد)
  paymentCreditAccount  Account  @relation("PaymentCreditAccount", fields: [paymentCreditAccountId], references: [id])
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  @@map("sales_journal_settings")
}
