/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/(dashboard)/page";
exports.ids = ["app/[locale]/(dashboard)/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./ar.json": [
		"(rsc)/./messages/ar.json",
		"_rsc_messages_ar_json"
	],
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2F(dashboard)%2Fpage&page=%2F%5Blocale%5D%2F(dashboard)%2Fpage&appPaths=%2F%5Blocale%5D%2F(dashboard)%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2F(dashboard)%2Fpage.tsx&appDir=C%3A%5CUsers%5Chossa%5CDownloads%5CSMART%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chossa%5CDownloads%5CSMART&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2F(dashboard)%2Fpage&page=%2F%5Blocale%5D%2F(dashboard)%2Fpage&appPaths=%2F%5Blocale%5D%2F(dashboard)%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2F(dashboard)%2Fpage.tsx&appDir=C%3A%5CUsers%5Chossa%5CDownloads%5CSMART%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chossa%5CDownloads%5CSMART&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\"));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/error.tsx */ \"(rsc)/./src/app/[locale]/error.tsx\"));\nconst module7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/(dashboard)/layout.tsx */ \"(rsc)/./src/app/[locale]/(dashboard)/layout.tsx\"));\nconst page8 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/(dashboard)/page.tsx */ \"(rsc)/./src/app/[locale]/(dashboard)/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        '(dashboard)',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page8, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module7, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\layout.tsx\"],\n'error': [module6, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\error.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\layout.tsx\"],\n'error': [module1, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\error.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/(dashboard)/page\",\n        pathname: \"/[locale]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2F(dashboard)%2Fpage&page=%2F%5Blocale%5D%2F(dashboard)%2Fpage&appPaths=%2F%5Blocale%5D%2F(dashboard)%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2F(dashboard)%2Fpage.tsx&appDir=C%3A%5CUsers%5Chossa%5CDownloads%5CSMART%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chossa%5CDownloads%5CSMART&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0LWludGwlNUMlNUNkaXN0JTVDJTVDZXNtJTVDJTVDc2hhcmVkJTVDJTVDTmV4dEludGxDbGllbnRQcm92aWRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDaG9zc2ElNUMlNUNEb3dubG9hZHMlNUMlNUNTTUFSVCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnUEFBMksiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxob3NzYVxcXFxEb3dubG9hZHNcXFxcU01BUlRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHQtaW50bFxcXFxkaXN0XFxcXGVzbVxcXFxzaGFyZWRcXFxcTmV4dEludGxDbGllbnRQcm92aWRlci5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(dashboard)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(dashboard)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/(dashboard)/page.tsx */ \"(rsc)/./src/app/[locale]/(dashboard)/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMlNUJsb2NhbGUlNUQlNUMlNUMoZGFzaGJvYXJkKSU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTEFBa0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGhvc3NhXFxcXERvd25sb2Fkc1xcXFxTTUFSVFxcXFxzcmNcXFxcYXBwXFxcXFtsb2NhbGVdXFxcXChkYXNoYm9hcmQpXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(dashboard)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/error.tsx */ \"(rsc)/./src/app/[locale]/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMlNUJsb2NhbGUlNUQlNUMlNUNlcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUFzRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaG9zc2FcXFxcRG93bmxvYWRzXFxcXFNNQVJUXFxcXHNyY1xcXFxhcHBcXFxcW2xvY2FsZV1cXFxcZXJyb3IudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNlcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUE0RiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaG9zc2FcXFxcRG93bmxvYWRzXFxcXFNNQVJUXFxcXHNyY1xcXFxhcHBcXFxcZXJyb3IudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(rsc)/./src/components/layout/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Sidebar.tsx */ \"(rsc)/./src/components/layout/Sidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0JTVDJTVDSGVhZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDaG9zc2ElNUMlNUNEb3dubG9hZHMlNUMlNUNTTUFSVCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQlNUMlNUNTaWRlYmFyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdMQUF5STtBQUN6STtBQUNBLGtMQUEwSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGhvc3NhXFxcXERvd25sb2Fkc1xcXFxTTUFSVFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxsYXlvdXRcXFxcSGVhZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGhvc3NhXFxcXERvd25sb2Fkc1xcXFxTTUFSVFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxsYXlvdXRcXFxcU2lkZWJhci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/[locale]/(dashboard)/layout.tsx":
/*!*************************************************!*\
  !*** ./src/app/[locale]/(dashboard)/layout.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/Sidebar */ \"(rsc)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n\n\n\nasync function DashboardLayout({ children, params }) {\n    const { locale } = await params;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                locale: locale\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\layout.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        locale: locale\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\layout.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\layout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\layout.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\layout.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsb2NhbGVdLyhkYXNoYm9hcmQpL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQWtEO0FBQ0Y7QUFFakMsZUFBZUUsZ0JBQWdCLEVBQzVDQyxRQUFRLEVBQ1JDLE1BQU0sRUFJUDtJQUNDLE1BQU0sRUFBRUMsTUFBTSxFQUFFLEdBQUcsTUFBTUQ7SUFDekIscUJBQ0UsOERBQUNFO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDUCxrRUFBT0E7Z0JBQUNLLFFBQVFBOzs7Ozs7MEJBQ2pCLDhEQUFDQztnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNOLGlFQUFNQTt3QkFBQ0ksUUFBUUE7Ozs7OztrQ0FDaEIsOERBQUNHO3dCQUFLRCxXQUFVO2tDQUNiSjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaG9zc2FcXERvd25sb2Fkc1xcU01BUlRcXHNyY1xcYXBwXFxbbG9jYWxlXVxcKGRhc2hib2FyZClcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFNpZGViYXIgZnJvbSAnQC9jb21wb25lbnRzL2xheW91dC9TaWRlYmFyJztcbmltcG9ydCBIZWFkZXIgZnJvbSAnQC9jb21wb25lbnRzL2xheW91dC9IZWFkZXInO1xuXG5leHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiBEYXNoYm9hcmRMYXlvdXQoe1xuICBjaGlsZHJlbixcbiAgcGFyYW1zXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG4gIHBhcmFtczogUHJvbWlzZTx7IGxvY2FsZTogc3RyaW5nIH0+O1xufSkge1xuICBjb25zdCB7IGxvY2FsZSB9ID0gYXdhaXQgcGFyYW1zO1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICA8U2lkZWJhciBsb2NhbGU9e2xvY2FsZX0gLz5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2wgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIDxIZWFkZXIgbG9jYWxlPXtsb2NhbGV9IC8+XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gcC02XCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L21haW4+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTaWRlYmFyIiwiSGVhZGVyIiwiRGFzaGJvYXJkTGF5b3V0IiwiY2hpbGRyZW4iLCJwYXJhbXMiLCJsb2NhbGUiLCJkaXYiLCJjbGFzc05hbWUiLCJtYWluIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/(dashboard)/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/(dashboard)/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/[locale]/(dashboard)/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\(dashboard)\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/[locale]/error.tsx":
/*!************************************!*\
  !*** ./src/app/[locale]/error.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\n\nconst locales = [\n    'ar',\n    'en'\n];\nconst metadata = {\n    title: 'Smart ERP - نظام سمارت للحلول المحاسبية',\n    description: 'نظام ERP متكامل للحلول المحاسبية والإدارية'\n};\nasync function LocaleLayout({ children, params }) {\n    const { locale } = await params;\n    // Validate that the incoming `locale` parameter is valid\n    if (!locales.includes(locale)) (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    // Providing all messages to the client\n    // side is the easiest way to get started\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        dir: locale === 'ar' ? 'rtl' : 'ltr',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: locale === 'ar' ? 'font-arabic' : 'font-english',\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                messages: messages,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsb2NhbGVdL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQ21EO0FBQ0o7QUFDSjtBQUNuQjtBQUV4QixNQUFNRyxVQUFVO0lBQUM7SUFBTTtDQUFLO0FBRXJCLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsZUFBZUMsYUFBYSxFQUN6Q0MsUUFBUSxFQUNSQyxNQUFNLEVBSVA7SUFDQyxNQUFNLEVBQUVDLE1BQU0sRUFBRSxHQUFHLE1BQU1EO0lBQ3pCLHlEQUF5RDtJQUN6RCxJQUFJLENBQUNOLFFBQVFRLFFBQVEsQ0FBQ0QsU0FBZ0JSLHlEQUFRQTtJQUU5Qyx1Q0FBdUM7SUFDdkMseUNBQXlDO0lBQ3pDLE1BQU1VLFdBQVcsTUFBTVgsNERBQVdBO0lBRWxDLHFCQUNFLDhEQUFDWTtRQUFLQyxNQUFNSjtRQUFRSyxLQUFLTCxXQUFXLE9BQU8sUUFBUTtrQkFDakQsNEVBQUNNO1lBQUtDLFdBQVdQLFdBQVcsT0FBTyxnQkFBZ0I7c0JBQ2pELDRFQUFDVixpREFBc0JBO2dCQUFDWSxVQUFVQTswQkFDL0JKOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaG9zc2FcXERvd25sb2Fkc1xcU01BUlRcXHNyY1xcYXBwXFxbbG9jYWxlXVxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgeyBOZXh0SW50bENsaWVudFByb3ZpZGVyIH0gZnJvbSAnbmV4dC1pbnRsJztcbmltcG9ydCB7IGdldE1lc3NhZ2VzIH0gZnJvbSAnbmV4dC1pbnRsL3NlcnZlcic7XG5pbXBvcnQgeyBub3RGb3VuZCB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgJy4uL2dsb2JhbHMuY3NzJztcblxuY29uc3QgbG9jYWxlcyA9IFsnYXInLCAnZW4nXTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdTbWFydCBFUlAgLSDZhti42KfZhSDYs9mF2KfYsdiqINmE2YTYrdmE2YjZhCDYp9mE2YXYrdin2LPYqNmK2KknLFxuICBkZXNjcmlwdGlvbjogJ9mG2LjYp9mFIEVSUCDZhdiq2YPYp9mF2YQg2YTZhNit2YTZiNmEINin2YTZhdit2KfYs9io2YrYqSDZiNin2YTYpdiv2KfYsdmK2KknLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gTG9jYWxlTGF5b3V0KHtcbiAgY2hpbGRyZW4sXG4gIHBhcmFtc1xufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICBwYXJhbXM6IFByb21pc2U8eyBsb2NhbGU6IHN0cmluZyB9Pjtcbn0pIHtcbiAgY29uc3QgeyBsb2NhbGUgfSA9IGF3YWl0IHBhcmFtcztcbiAgLy8gVmFsaWRhdGUgdGhhdCB0aGUgaW5jb21pbmcgYGxvY2FsZWAgcGFyYW1ldGVyIGlzIHZhbGlkXG4gIGlmICghbG9jYWxlcy5pbmNsdWRlcyhsb2NhbGUgYXMgYW55KSkgbm90Rm91bmQoKTtcblxuICAvLyBQcm92aWRpbmcgYWxsIG1lc3NhZ2VzIHRvIHRoZSBjbGllbnRcbiAgLy8gc2lkZSBpcyB0aGUgZWFzaWVzdCB3YXkgdG8gZ2V0IHN0YXJ0ZWRcbiAgY29uc3QgbWVzc2FnZXMgPSBhd2FpdCBnZXRNZXNzYWdlcygpO1xuXG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz17bG9jYWxlfSBkaXI9e2xvY2FsZSA9PT0gJ2FyJyA/ICdydGwnIDogJ2x0cid9PlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtsb2NhbGUgPT09ICdhcicgPyAnZm9udC1hcmFiaWMnIDogJ2ZvbnQtZW5nbGlzaCd9PlxuICAgICAgICA8TmV4dEludGxDbGllbnRQcm92aWRlciBtZXNzYWdlcz17bWVzc2FnZXN9PlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9OZXh0SW50bENsaWVudFByb3ZpZGVyPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJOZXh0SW50bENsaWVudFByb3ZpZGVyIiwiZ2V0TWVzc2FnZXMiLCJub3RGb3VuZCIsImxvY2FsZXMiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJMb2NhbGVMYXlvdXQiLCJjaGlsZHJlbiIsInBhcmFtcyIsImxvY2FsZSIsImluY2x1ZGVzIiwibWVzc2FnZXMiLCJodG1sIiwibGFuZyIsImRpciIsImJvZHkiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"26ab445da83f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhvc3NhXFxEb3dubG9hZHNcXFNNQVJUXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyNmFiNDQ1ZGE4M2ZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\nconst metadata = {\n    title: 'Smart ERP - نظام سمارت للحلول المحاسبية',\n    description: 'نظام ERP متكامل للحلول المحاسبية والإدارية'\n};\nfunction RootLayout({ children }) {\n    // Redirect to default locale\n    (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect)('/ar');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQzJDO0FBRXBDLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MsNkJBQTZCO0lBQzdCTCx5REFBUUEsQ0FBQztBQUNYIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhvc3NhXFxEb3dubG9hZHNcXFNNQVJUXFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgeyByZWRpcmVjdCB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnU21hcnQgRVJQIC0g2YbYuNin2YUg2LPZhdin2LHYqiDZhNmE2K3ZhNmI2YQg2KfZhNmF2K3Yp9iz2KjZitipJyxcbiAgZGVzY3JpcHRpb246ICfZhti42KfZhSBFUlAg2YXYqtmD2KfZhdmEINmE2YTYrdmE2YjZhCDYp9mE2YXYrdin2LPYqNmK2Kkg2YjYp9mE2KXYr9in2LHZitipJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgLy8gUmVkaXJlY3QgdG8gZGVmYXVsdCBsb2NhbGVcbiAgcmVkaXJlY3QoJy9hcicpO1xufVxuIl0sIm5hbWVzIjpbInJlZGlyZWN0IiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\SMART\\src\\components\\layout\\Header.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\SMART\\src\\components\\layout\\Sidebar.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/i18n.ts":
/*!*********************!*\
  !*** ./src/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n\n\n// Can be imported from a shared config\nconst locales = [\n    'ar',\n    'en'\n];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ requestLocale })=>{\n    // Get the locale from the request\n    let locale = requestLocale || 'ar';\n    // Validate that the incoming `locale` parameter is valid\n    if (!locales.includes(locale)) (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMkM7QUFDUztBQUVwRCx1Q0FBdUM7QUFDdkMsTUFBTUUsVUFBVTtJQUFDO0lBQU07Q0FBSztBQUU1QixpRUFBZUQsNERBQWdCQSxDQUFDLE9BQU8sRUFBRUUsYUFBYSxFQUFFO0lBQ3RELGtDQUFrQztJQUNsQyxJQUFJQyxTQUFTRCxpQkFBaUI7SUFFOUIseURBQXlEO0lBQ3pELElBQUksQ0FBQ0QsUUFBUUcsUUFBUSxDQUFDRCxTQUFnQkoseURBQVFBO0lBRTlDLE9BQU87UUFDTEk7UUFDQUUsVUFBVSxDQUFDLE1BQU0seUVBQU8sR0FBYSxFQUFFRixPQUFPLE1BQU0sR0FBR0csT0FBTztJQUNoRTtBQUNGLEVBQUUsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxob3NzYVxcRG93bmxvYWRzXFxTTUFSVFxcc3JjXFxpMThuLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG5vdEZvdW5kIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IGdldFJlcXVlc3RDb25maWcgfSBmcm9tICduZXh0LWludGwvc2VydmVyJztcblxuLy8gQ2FuIGJlIGltcG9ydGVkIGZyb20gYSBzaGFyZWQgY29uZmlnXG5jb25zdCBsb2NhbGVzID0gWydhcicsICdlbiddO1xuXG5leHBvcnQgZGVmYXVsdCBnZXRSZXF1ZXN0Q29uZmlnKGFzeW5jICh7IHJlcXVlc3RMb2NhbGUgfSkgPT4ge1xuICAvLyBHZXQgdGhlIGxvY2FsZSBmcm9tIHRoZSByZXF1ZXN0XG4gIGxldCBsb2NhbGUgPSByZXF1ZXN0TG9jYWxlIHx8ICdhcic7XG5cbiAgLy8gVmFsaWRhdGUgdGhhdCB0aGUgaW5jb21pbmcgYGxvY2FsZWAgcGFyYW1ldGVyIGlzIHZhbGlkXG4gIGlmICghbG9jYWxlcy5pbmNsdWRlcyhsb2NhbGUgYXMgYW55KSkgbm90Rm91bmQoKTtcblxuICByZXR1cm4ge1xuICAgIGxvY2FsZSxcbiAgICBtZXNzYWdlczogKGF3YWl0IGltcG9ydChgLi4vbWVzc2FnZXMvJHtsb2NhbGV9Lmpzb25gKSkuZGVmYXVsdFxuICB9O1xufSk7XG4iXSwibmFtZXMiOlsibm90Rm91bmQiLCJnZXRSZXF1ZXN0Q29uZmlnIiwibG9jYWxlcyIsInJlcXVlc3RMb2NhbGUiLCJsb2NhbGUiLCJpbmNsdWRlcyIsIm1lc3NhZ2VzIiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNob3NzYSU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1NNQVJUJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDaG9zc2ElNUMlNUNEb3dubG9hZHMlNUMlNUNTTUFSVCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBcUk7QUFDckk7QUFDQSwwT0FBd0k7QUFDeEk7QUFDQSwwT0FBd0k7QUFDeEk7QUFDQSxvUkFBOEo7QUFDOUo7QUFDQSx3T0FBdUk7QUFDdkk7QUFDQSw0UEFBa0o7QUFDbEo7QUFDQSxrUUFBcUo7QUFDcko7QUFDQSxzUUFBc0oiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGhvc3NhXFxcXERvd25sb2Fkc1xcXFxTTUFSVFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxob3NzYVxcXFxEb3dubG9hZHNcXFxcU01BUlRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaG9zc2FcXFxcRG93bmxvYWRzXFxcXFNNQVJUXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGhvc3NhXFxcXERvd25sb2Fkc1xcXFxTTUFSVFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxob3NzYVxcXFxEb3dubG9hZHNcXFxcU01BUlRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxob3NzYVxcXFxEb3dubG9hZHNcXFxcU01BUlRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaG9zc2FcXFxcRG93bmxvYWRzXFxcXFNNQVJUXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGhvc3NhXFxcXERvd25sb2Fkc1xcXFxTTUFSVFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0LWludGwlNUMlNUNkaXN0JTVDJTVDZXNtJTVDJTVDc2hhcmVkJTVDJTVDTmV4dEludGxDbGllbnRQcm92aWRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDaG9zc2ElNUMlNUNEb3dubG9hZHMlNUMlNUNTTUFSVCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnUEFBMksiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxob3NzYVxcXFxEb3dubG9hZHNcXFxcU01BUlRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHQtaW50bFxcXFxkaXN0XFxcXGVzbVxcXFxzaGFyZWRcXFxcTmV4dEludGxDbGllbnRQcm92aWRlci5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(dashboard)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(dashboard)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/(dashboard)/page.tsx */ \"(ssr)/./src/app/[locale]/(dashboard)/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMlNUJsb2NhbGUlNUQlNUMlNUMoZGFzaGJvYXJkKSU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTEFBa0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGhvc3NhXFxcXERvd25sb2Fkc1xcXFxTTUFSVFxcXFxzcmNcXFxcYXBwXFxcXFtsb2NhbGVdXFxcXChkYXNoYm9hcmQpXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(dashboard)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/error.tsx */ \"(ssr)/./src/app/[locale]/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMlNUJsb2NhbGUlNUQlNUMlNUNlcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUFzRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaG9zc2FcXFxcRG93bmxvYWRzXFxcXFNNQVJUXFxcXHNyY1xcXFxhcHBcXFxcW2xvY2FsZV1cXFxcZXJyb3IudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNlcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUE0RiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaG9zc2FcXFxcRG93bmxvYWRzXFxcXFNNQVJUXFxcXHNyY1xcXFxhcHBcXFxcZXJyb3IudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Sidebar.tsx */ \"(ssr)/./src/components/layout/Sidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hvc3NhJTVDJTVDRG93bmxvYWRzJTVDJTVDU01BUlQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0JTVDJTVDSGVhZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDaG9zc2ElNUMlNUNEb3dubG9hZHMlNUMlNUNTTUFSVCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQlNUMlNUNTaWRlYmFyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdMQUF5STtBQUN6STtBQUNBLGtMQUEwSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGhvc3NhXFxcXERvd25sb2Fkc1xcXFxTTUFSVFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxsYXlvdXRcXFxcSGVhZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGhvc3NhXFxcXERvd25sb2Fkc1xcXFxTTUFSVFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxsYXlvdXRcXFxcU2lkZWJhci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chossa%5C%5CDownloads%5C%5CSMART%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/[locale]/(dashboard)/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/[locale]/(dashboard)/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,DollarSign,Package,ShoppingCart,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Cell.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DashboardPage() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations)();\n    // Mock data for charts\n    const salesData = [\n        {\n            month: 'يناير',\n            sales: 4000,\n            expenses: 2400\n        },\n        {\n            month: 'فبراير',\n            sales: 3000,\n            expenses: 1398\n        },\n        {\n            month: 'مارس',\n            sales: 2000,\n            expenses: 9800\n        },\n        {\n            month: 'أبريل',\n            sales: 2780,\n            expenses: 3908\n        },\n        {\n            month: 'مايو',\n            sales: 1890,\n            expenses: 4800\n        },\n        {\n            month: 'يونيو',\n            sales: 2390,\n            expenses: 3800\n        }\n    ];\n    const expenseData = [\n        {\n            name: 'الرواتب',\n            value: 400,\n            color: '#0088FE'\n        },\n        {\n            name: 'الإيجار',\n            value: 300,\n            color: '#00C49F'\n        },\n        {\n            name: 'المرافق',\n            value: 200,\n            color: '#FFBB28'\n        },\n        {\n            name: 'أخرى',\n            value: 100,\n            color: '#FF8042'\n        }\n    ];\n    const recentTransactions = [\n        {\n            id: 1,\n            description: 'مبيعات نقدية',\n            amount: 1500,\n            type: 'credit',\n            date: '2024-01-15'\n        },\n        {\n            id: 2,\n            description: 'دفع إيجار',\n            amount: 800,\n            type: 'debit',\n            date: '2024-01-14'\n        },\n        {\n            id: 3,\n            description: 'شراء معدات',\n            amount: 2500,\n            type: 'debit',\n            date: '2024-01-13'\n        },\n        {\n            id: 4,\n            description: 'إيرادات خدمات',\n            amount: 1200,\n            type: 'credit',\n            date: '2024-01-12'\n        }\n    ];\n    const stats = [\n        {\n            title: t('dashboard.totalSales'),\n            value: '125,000',\n            change: '+12%',\n            trend: 'up',\n            icon: _barrel_optimize_names_CreditCard_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            color: 'text-green-600',\n            bgColor: 'bg-green-100'\n        },\n        {\n            title: t('dashboard.totalPurchases'),\n            value: '85,000',\n            change: '+8%',\n            trend: 'up',\n            icon: _barrel_optimize_names_CreditCard_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: 'text-blue-600',\n            bgColor: 'bg-blue-100'\n        },\n        {\n            title: t('dashboard.totalExpenses'),\n            value: '45,000',\n            change: '-5%',\n            trend: 'down',\n            icon: _barrel_optimize_names_CreditCard_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: 'text-red-600',\n            bgColor: 'bg-red-100'\n        },\n        {\n            title: t('dashboard.netProfit'),\n            value: '35,000',\n            change: '+15%',\n            trend: 'up',\n            icon: _barrel_optimize_names_CreditCard_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'text-purple-600',\n            bgColor: 'bg-purple-100'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-primary-600 to-primary-800 rounded-lg p-6 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: t('dashboard.welcome')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-primary-100\",\n                        children: new Date().toLocaleDateString('ar-LY', {\n                            weekday: 'long',\n                            year: 'numeric',\n                            month: 'long',\n                            day: 'numeric'\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: stats.map((stat, index)=>{\n                    const Icon = stat.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: stat.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900 mt-1\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-2\",\n                                            children: [\n                                                stat.trend === 'up' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_DollarSign_Package_ShoppingCart_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 text-red-500 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm font-medium ${stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}`,\n                                                    children: stat.change\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-3 rounded-full ${stat.bgColor}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: `w-6 h-6 ${stat.color}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 15\n                        }, this)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: t('dashboard.monthlyComparison')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.ResponsiveContainer, {\n                                width: \"100%\",\n                                height: 300,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.BarChart, {\n                                    data: salesData,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.CartesianGrid, {\n                                            strokeDasharray: \"3 3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.XAxis, {\n                                            dataKey: \"month\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.YAxis, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.Bar, {\n                                            dataKey: \"sales\",\n                                            fill: \"#3b82f6\",\n                                            name: \"المبيعات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.Bar, {\n                                            dataKey: \"expenses\",\n                                            fill: \"#ef4444\",\n                                            name: \"المصروفات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: t('dashboard.expensesChart')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.ResponsiveContainer, {\n                                width: \"100%\",\n                                height: 300,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.PieChart, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.Pie, {\n                                            data: expenseData,\n                                            cx: \"50%\",\n                                            cy: \"50%\",\n                                            labelLine: false,\n                                            label: ({ name, percent })=>`${name} ${(percent * 100).toFixed(0)}%`,\n                                            outerRadius: 80,\n                                            fill: \"#8884d8\",\n                                            dataKey: \"value\",\n                                            children: expenseData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__.Cell, {\n                                                    fill: entry.color\n                                                }, `cell-${index}`, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: t('dashboard.recentTransactions')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"table-header px-6 py-3 text-right\",\n                                                children: t('common.description')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"table-header px-6 py-3 text-right\",\n                                                children: t('common.amount')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"table-header px-6 py-3 text-right\",\n                                                children: t('common.type')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"table-header px-6 py-3 text-right\",\n                                                children: t('common.date')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: recentTransactions.map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"table-cell\",\n                                                    children: transaction.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"table-cell\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `font-medium ${transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'}`,\n                                                        children: [\n                                                            transaction.type === 'credit' ? '+' : '-',\n                                                            transaction.amount.toLocaleString(),\n                                                            \" د.ل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"table-cell\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${transaction.type === 'credit' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                                                        children: transaction.type === 'credit' ? 'دائن' : 'مدين'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"table-cell\",\n                                                    children: new Date(transaction.date).toLocaleDateString('ar-LY')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, transaction.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/(dashboard)/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/error.tsx":
/*!************************************!*\
  !*** ./src/app/[locale]/error.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Error({ error, reset }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: 32,\n            textAlign: \"center\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                style: {\n                    color: \"red\"\n                },\n                children: \"حدث خطأ غير متوقع\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\error.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: error.message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\error.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: reset,\n                style: {\n                    marginTop: 16,\n                    padding: 8\n                },\n                children: \"إعادة المحاولة\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\error.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\error.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1tsb2NhbGVdL2Vycm9yLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDMEI7QUFFWCxTQUFTQyxNQUFNLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUF1QztJQUNqRixxQkFDRSw4REFBQ0M7UUFBSUMsT0FBTztZQUFFQyxTQUFTO1lBQUlDLFdBQVc7UUFBUzs7MEJBQzdDLDhEQUFDQztnQkFBR0gsT0FBTztvQkFBRUksT0FBTztnQkFBTTswQkFBRzs7Ozs7OzBCQUM3Qiw4REFBQ0M7MEJBQUdSLE1BQU1TLE9BQU87Ozs7OzswQkFDakIsOERBQUNDO2dCQUFPQyxTQUFTVjtnQkFBT0UsT0FBTztvQkFBRVMsV0FBVztvQkFBSVIsU0FBUztnQkFBRTswQkFBRzs7Ozs7Ozs7Ozs7O0FBS3BFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhvc3NhXFxEb3dubG9hZHNcXFNNQVJUXFxzcmNcXGFwcFxcW2xvY2FsZV1cXGVycm9yLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRXJyb3IoeyBlcnJvciwgcmVzZXQgfTogeyBlcnJvcjogRXJyb3I7IHJlc2V0OiAoKSA9PiB2b2lkIH0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBzdHlsZT17eyBwYWRkaW5nOiAzMiwgdGV4dEFsaWduOiBcImNlbnRlclwiIH19PlxyXG4gICAgICA8aDIgc3R5bGU9e3sgY29sb3I6IFwicmVkXCIgfX0+2K3Yr9irINiu2LfYoyDYutmK2LEg2YXYqtmI2YLYuTwvaDI+XHJcbiAgICAgIDxwPntlcnJvci5tZXNzYWdlfTwvcD5cclxuICAgICAgPGJ1dHRvbiBvbkNsaWNrPXtyZXNldH0gc3R5bGU9e3sgbWFyZ2luVG9wOiAxNiwgcGFkZGluZzogOCB9fT5cclxuICAgICAgICDYpdi52KfYr9ipINin2YTZhdit2KfZiNmE2KlcclxuICAgICAgPC9idXR0b24+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkVycm9yIiwiZXJyb3IiLCJyZXNldCIsImRpdiIsInN0eWxlIiwicGFkZGluZyIsInRleHRBbGlnbiIsImgyIiwiY29sb3IiLCJwIiwibWVzc2FnZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJtYXJnaW5Ub3AiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Error({ error, reset }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: 32,\n            textAlign: \"center\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                style: {\n                    color: \"red\"\n                },\n                children: \"حدث خطأ غير متوقع\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\error.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: error.message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\error.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: reset,\n                style: {\n                    marginTop: 16,\n                    padding: 8\n                },\n                children: \"إعادة المحاولة\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\error.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Vycm9yLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDMEI7QUFFWCxTQUFTQyxNQUFNLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUF1QztJQUNqRixxQkFDRSw4REFBQ0M7UUFBSUMsT0FBTztZQUFFQyxTQUFTO1lBQUlDLFdBQVc7UUFBUzs7MEJBQzdDLDhEQUFDQztnQkFBR0gsT0FBTztvQkFBRUksT0FBTztnQkFBTTswQkFBRzs7Ozs7OzBCQUM3Qiw4REFBQ0M7MEJBQUdSLE1BQU1TLE9BQU87Ozs7OzswQkFDakIsOERBQUNDO2dCQUFPQyxTQUFTVjtnQkFBT0UsT0FBTztvQkFBRVMsV0FBVztvQkFBSVIsU0FBUztnQkFBRTswQkFBRzs7Ozs7Ozs7Ozs7O0FBS3BFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhvc3NhXFxEb3dubG9hZHNcXFNNQVJUXFxzcmNcXGFwcFxcZXJyb3IudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBFcnJvcih7IGVycm9yLCByZXNldCB9OiB7IGVycm9yOiBFcnJvcjsgcmVzZXQ6ICgpID0+IHZvaWQgfSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IHN0eWxlPXt7IHBhZGRpbmc6IDMyLCB0ZXh0QWxpZ246IFwiY2VudGVyXCIgfX0+XHJcbiAgICAgIDxoMiBzdHlsZT17eyBjb2xvcjogXCJyZWRcIiB9fT7Yrdiv2Ksg2K7Yt9ijINi62YrYsSDZhdiq2YjZgti5PC9oMj5cclxuICAgICAgPHA+e2Vycm9yLm1lc3NhZ2V9PC9wPlxyXG4gICAgICA8YnV0dG9uIG9uQ2xpY2s9e3Jlc2V0fSBzdHlsZT17eyBtYXJnaW5Ub3A6IDE2LCBwYWRkaW5nOiA4IH19PlxyXG4gICAgICAgINil2LnYp9iv2Kkg2KfZhNmF2K3Yp9mI2YTYqVxyXG4gICAgICA8L2J1dHRvbj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiRXJyb3IiLCJlcnJvciIsInJlc2V0IiwiZGl2Iiwic3R5bGUiLCJwYWRkaW5nIiwidGV4dEFsaWduIiwiaDIiLCJjb2xvciIsInAiLCJtZXNzYWdlIiwiYnV0dG9uIiwib25DbGljayIsIm1hcmdpblRvcCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Header({ locale }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [showLanguageMenu, setShowLanguageMenu] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const switchLocale = (newLocale)=>{\n        const newPath = pathname.replace(`/${locale}`, `/${newLocale}`);\n        return newPath;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-200 h-16 flex items-center justify-between px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-lg font-semibold text-gray-800\",\n                    children: [\n                        pathname.includes('/accounts') && t('navigation.accounts'),\n                        pathname.includes('/sales') && t('navigation.sales'),\n                        pathname.includes('/purchases') && t('navigation.purchases'),\n                        pathname.includes('/inventory') && t('navigation.inventory'),\n                        pathname.includes('/payroll') && t('navigation.payroll'),\n                        pathname.includes('/payments') && t('navigation.payments'),\n                        pathname.includes('/receipts') && t('navigation.receipts'),\n                        pathname.includes('/settings') && t('navigation.settings'),\n                        pathname === `/${locale}` && t('navigation.dashboard')\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowLanguageMenu(!showLanguageMenu),\n                                className: \"flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: locale === 'ar' ? 'العربية' : 'English'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            showLanguageMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-0 rtl:right-auto rtl:left-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: switchLocale('ar'),\n                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-t-md\",\n                                        onClick: ()=>setShowLanguageMenu(false),\n                                        children: \"العربية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: switchLocale('en'),\n                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-b-md\",\n                                        onClick: ()=>setShowLanguageMenu(false),\n                                        children: \"English\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"p-2 rounded-md hover:bg-gray-100 transition-colors duration-200 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: locale === 'ar' ? 'المدير' : 'Admin'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,Package,Receipt,Settings,ShoppingCart,Users,Warehouse!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,Package,Receipt,Settings,ShoppingCart,Users,Warehouse!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,Package,Receipt,Settings,ShoppingCart,Users,Warehouse!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,Package,Receipt,Settings,ShoppingCart,Users,Warehouse!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,Package,Receipt,Settings,ShoppingCart,Users,Warehouse!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/warehouse.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,Package,Receipt,Settings,ShoppingCart,Users,Warehouse!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,Package,Receipt,Settings,ShoppingCart,Users,Warehouse!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,Package,Receipt,Settings,ShoppingCart,Users,Warehouse!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,Package,Receipt,Settings,ShoppingCart,Users,Warehouse!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,Package,Receipt,Settings,ShoppingCart,Users,Warehouse!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,Package,Receipt,Settings,ShoppingCart,Users,Warehouse!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,Package,Receipt,Settings,ShoppingCart,Users,Warehouse!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Sidebar({ locale }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [expandedMenus, setExpandedMenus] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([\n        'accounts'\n    ]);\n    const toggleMenu = (menuId)=>{\n        setExpandedMenus((prev)=>prev.includes(menuId) ? prev.filter((id)=>id !== menuId) : [\n                ...prev,\n                menuId\n            ]);\n    };\n    const menuItems = [\n        {\n            id: 'dashboard',\n            label: t('navigation.dashboard'),\n            href: `/${locale}`,\n            icon: _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: 'accounts',\n            label: t('navigation.accounts'),\n            icon: _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            children: [\n                {\n                    label: t('accounts.chartOfAccounts'),\n                    href: `/${locale}/accounts/chart`\n                },\n                {\n                    label: t('accounts.journalEntries'),\n                    href: `/${locale}/accounts/journals`\n                },\n                {\n                    label: t('accounts.financialStatements'),\n                    href: `/${locale}/accounts/reports`\n                }\n            ]\n        },\n        {\n            id: 'sales',\n            label: t('navigation.sales'),\n            href: `/${locale}/sales`,\n            icon: _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            disabled: true\n        },\n        {\n            id: 'purchases',\n            label: t('navigation.purchases'),\n            href: `/${locale}/purchases`,\n            icon: _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            disabled: true\n        },\n        {\n            id: 'inventory',\n            label: t('navigation.inventory'),\n            href: `/${locale}/inventory`,\n            icon: _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            disabled: true\n        },\n        {\n            id: 'payroll',\n            label: t('navigation.payroll'),\n            href: `/${locale}/payroll`,\n            icon: _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            disabled: true\n        },\n        {\n            id: 'payments',\n            label: t('navigation.payments'),\n            href: `/${locale}/payments`,\n            icon: _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            disabled: true\n        },\n        {\n            id: 'receipts',\n            label: t('navigation.receipts'),\n            href: `/${locale}/receipts`,\n            icon: _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            disabled: true\n        },\n        {\n            id: 'reports',\n            label: t('navigation.reports'),\n            href: `/${locale}/reports`,\n            icon: _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            disabled: true\n        },\n        {\n            id: 'settings',\n            label: t('navigation.settings'),\n            href: `/${locale}/settings`,\n            icon: _barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-64 bg-white shadow-lg h-screen overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-bold text-gray-800\",\n                        children: \"Smart ERP\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mt-1\",\n                        children: locale === 'ar' ? 'نظام الحلول المحاسبية' : 'Accounting Solutions'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"space-y-1\",\n                    children: menuItems.map((item)=>{\n                        const isActive = item.href ? pathname === item.href : false;\n                        const isExpanded = expandedMenus.includes(item.id);\n                        const Icon = item.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: item.children ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>toggleMenu(item.id),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('w-full flex items-center justify-between px-6 py-3 text-sm font-medium transition-colors duration-200', 'hover:bg-gray-50 text-gray-700'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-5 h-5 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    item.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 23\n                                            }, this),\n                                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 25\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_Package_Receipt_Settings_ShoppingCart_Users_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 21\n                                    }, this),\n                                    isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"bg-gray-50\",\n                                        children: item.children.map((child, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: child.href,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('block px-12 py-2 text-sm transition-colors duration-200', pathname === child.href ? 'text-primary-600 bg-primary-50 border-r-2 border-primary-600' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'),\n                                                    children: child.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 27\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center px-6 py-3 text-sm font-medium transition-colors duration-200', isActive ? 'text-primary-600 bg-primary-50 border-r-2 border-primary-600' : item.disabled ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'),\n                                onClick: item.disabled ? (e)=>e.preventDefault() : undefined,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-5 h-5 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 21\n                                    }, this),\n                                    item.label,\n                                    item.disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-auto text-xs bg-gray-200 text-gray-500 px-2 py-1 rounded\",\n                                        children: locale === 'ar' ? 'قريباً' : 'Soon'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 19\n                            }, this)\n                        }, item.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   generateAccountCode: () => (/* binding */ generateAccountCode),\n/* harmony export */   isRTL: () => (/* binding */ isRTL),\n/* harmony export */   validateAccountCode: () => (/* binding */ validateAccountCode)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = 'LYD') {\n    const currencySymbols = {\n        LYD: 'د.ل',\n        EGP: 'ج.م',\n        SAR: 'ر.س'\n    };\n    return `${amount.toLocaleString('ar-LY', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    })} ${currencySymbols[currency] || currency}`;\n}\nfunction formatDate(date, locale = 'ar') {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    if (locale === 'ar') {\n        return dateObj.toLocaleDateString('ar-LY');\n    }\n    return dateObj.toLocaleDateString('en-US');\n}\nfunction generateAccountCode(parentCode, level = 1) {\n    if (!parentCode) {\n        // Generate main account code (1000, 2000, etc.)\n        return `${level}000`;\n    }\n    // Generate sub-account code based on parent\n    const baseCode = parseInt(parentCode);\n    const increment = Math.pow(10, 4 - level);\n    return (baseCode + increment).toString();\n}\nfunction validateAccountCode(code) {\n    return /^\\d{4}$/.test(code);\n}\nfunction isRTL(locale) {\n    return locale === 'ar';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lodash","vendor-chunks/recharts","vendor-chunks/d3-shape","vendor-chunks/@formatjs","vendor-chunks/d3-scale","vendor-chunks/next-intl","vendor-chunks/lucide-react","vendor-chunks/d3-array","vendor-chunks/d3-format","vendor-chunks/use-intl","vendor-chunks/d3-interpolate","vendor-chunks/d3-time","vendor-chunks/react-smooth","vendor-chunks/intl-messageformat","vendor-chunks/react-transition-group","vendor-chunks/prop-types","vendor-chunks/@swc","vendor-chunks/@babel","vendor-chunks/recharts-scale","vendor-chunks/tslib","vendor-chunks/d3-time-format","vendor-chunks/d3-color","vendor-chunks/victory-vendor","vendor-chunks/tiny-invariant","vendor-chunks/tailwind-merge","vendor-chunks/internmap","vendor-chunks/fast-equals","vendor-chunks/decimal.js-light","vendor-chunks/d3-path","vendor-chunks/clsx","vendor-chunks/object-assign","vendor-chunks/eventemitter3"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2F(dashboard)%2Fpage&page=%2F%5Blocale%5D%2F(dashboard)%2Fpage&appPaths=%2F%5Blocale%5D%2F(dashboard)%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2F(dashboard)%2Fpage.tsx&appDir=C%3A%5CUsers%5Chossa%5CDownloads%5CSMART%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chossa%5CDownloads%5CSMART&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();