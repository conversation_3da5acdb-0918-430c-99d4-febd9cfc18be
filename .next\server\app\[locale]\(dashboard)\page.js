(()=>{var t={};t.id=321,t.ids=[321],t.modules={22:(t,e,r)=>{var n=r(75254),o=r(20623),i=r(48169),a=r(40542),c=r(45058);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):c(t)}},658:(t,e,r)=>{t.exports=r(41547)(r(85718),"Map")},1566:(t,e,r)=>{var n=r(89167),o=r(658),i=r(30401),a=r(34772),c=r(17830),u=r(29395),l=r(12290),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(n),v=l(o),m=l(i),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=d||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},1707:(t,e,r)=>{var n=r(35142),o=r(46436);t.exports=function(t,e){e=n(e,t);for(var r=0,i=e.length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},1944:t=>{t.exports=function(){return!1}},2408:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},2896:(t,e,r)=>{var n=r(81488),o=r(59467);t.exports=function(t,e){return null!=t&&o(t,e,n)}},2984:(t,e,r)=>{var n=r(49227);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},3105:t=>{t.exports=function(t){return t.split("")}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4999:(t,e,r)=>{t.exports=r(85718).Uint8Array},5231:(t,e,r)=>{var n=r(29395),o=r(55048);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},5359:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},5566:(t,e,r)=>{var n=r(41011),o=r(34117),i=r(66713),a=r(42403);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},6053:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},6330:t=>{t.exports=function(){return[]}},7383:(t,e,r)=>{var n=r(67009),o=r(32269),i=r(38428),a=r(55048);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(o(r)&&i(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},7651:(t,e,r)=>{var n=r(82038),o=r(52931),i=r(32269);t.exports=function(t){return i(t)?n(t):o(t)}},8336:(t,e,r)=>{var n=r(45803);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},8852:(t,e,r)=>{var n=r(1707);t.exports=function(t){return function(e){return n(e,t)}}},10034:(t,e,r)=>{var n=r(2984),o=r(22),i=r(46063);t.exports=function(t,e){return t&&t.length?n(t,o(e,2),i):void 0}},10090:(t,e,r)=>{var n=r(80458),o=r(89624),i=r(47282),a=i&&i.isTypedArray;t.exports=a?o(a):n},10653:(t,e,r)=>{var n=r(21456),o=r(63979),i=r(7651);t.exports=function(t){return n(t,i,o)}},10663:t=>{t.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},11424:(t,e,r)=>{var n=r(47603);t.exports=r(66400)(n)},11539:(t,e,r)=>{var n=r(37643),o=r(55048),i=r(49227),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):c.test(t)?a:+t}},12290:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},12344:(t,e,r)=>{t.exports=r(65984)()},14675:t=>{t.exports=function(t){return function(){return t}}},15451:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},15529:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>yh});var n={};r.r(n),r.d(n,{scaleBand:()=>nj,scaleDiverging:()=>function t(){var e=il(cP()(o0));return e.copy=function(){return cj(e,t())},nm.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=ig(cP()).domain([.1,1,10]);return e.copy=function(){return cj(e,t()).base(e.base())},nm.apply(e,arguments)},scaleDivergingPow:()=>cE,scaleDivergingSqrt:()=>ck,scaleDivergingSymlog:()=>function t(){var e=iw(cP());return e.copy=function(){return cj(e,t()).constant(e.constant())},nm.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,oJ),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,oJ):[0,1],il(n)},scaleImplicit:()=>nO,scaleLinear:()=>is,scaleLog:()=>function t(){let e=ig(o4()).domain([1,10]);return e.copy=()=>o3(e,t()).base(e.base()),nv.apply(e,arguments),e},scaleOrdinal:()=>nw,scalePoint:()=>nS,scalePow:()=>iE,scaleQuantile:()=>function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=oc){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[ol(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(on),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},nv.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[ol(i,t,0,o)]:e}function u(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return i.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},nv.apply(il(c),arguments)},scaleRadial:()=>function t(){var e,r=o6(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(iM(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,oJ)).map(iM)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},nv.apply(i,arguments),il(i)},scaleSequential:()=>function t(){var e=il(cw()(o0));return e.copy=function(){return cj(e,t())},nm.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=ig(cw()).domain([1,10]);return e.copy=function(){return cj(e,t()).base(e.base())},nm.apply(e,arguments)},scaleSequentialPow:()=>cS,scaleSequentialQuantile:()=>function t(){var e=[],r=o0;function n(t){if(null!=t&&!isNaN(t*=1))return r((ol(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(on),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return i_(t);if(e>=1)return iT(t);var n,o=(n-1)*e,i=Math.floor(o),a=iT((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?iC:function(t=on){if(t===on)return iC;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(o,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,i)}let a=e[r],c=n,u=o;for(iD(e,n,r),i(e[o],a)>0&&iD(e,n,o);c<u;){for(iD(e,c,u),++c,--u;0>i(e[c],a);)++c;for(;i(e[u],a)>0;)--u}0===i(e[n],a)?iD(e,n,u):iD(e,++u,o),u<=r&&(n=u+1),r<=u&&(o=u-1)}return e})(t,i).subarray(0,i+1));return a+(i_(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},nm.apply(n,arguments)},scaleSequentialSqrt:()=>cA,scaleSequentialSymlog:()=>function t(){var e=iw(cw());return e.copy=function(){return cj(e,t()).constant(e.constant())},nm.apply(e,arguments)},scaleSqrt:()=>ik,scaleSymlog:()=>function t(){var e=iw(o4());return e.copy=function(){return o3(e,t()).constant(e.constant())},nv.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[ol(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((r=Array.from(t)).length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},nv.apply(i,arguments)},scaleTime:()=>cx,scaleUtc:()=>cO,tickFormat:()=>iu});var o=r(60687),i=r(33213),a=r(28561),c=r(19080),u=r(85778),l=r(62688);let s=(0,l.A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),f=(0,l.A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),p=(0,l.A)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);var h=r(49384),d=r(43210),y=r.n(d),v=r(45603),m=r.n(v),b=r(63866),g=r.n(b),x=r(77822),O=r.n(x),w=r(40491),j=r.n(w),S=r(93490),A=r.n(S),P=r(37456),E=r.n(P),k=function(t){return 0===t?0:t>0?1:-1},M=function(t){return g()(t)&&t.indexOf("%")===t.length-1},T=function(t){return A()(t)&&!O()(t)},_=function(t){return T(t)||g()(t)},C=0,D=function(t){var e=++C;return"".concat(t||"").concat(e)},I=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!T(t)&&!g()(t))return n;if(M(t)){var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return O()(r)&&(r=n),o&&r>e&&(r=e),r},N=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},B=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},R=function(t,e){return T(t)&&T(e)?function(r){return t+r*(e-t)}:function(){return e}};function L(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):j()(t,e))===r}):null}var z=function(t,e){return T(t)&&T(e)?t-e:g()(t)&&g()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))},U=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},F=r(5231),$=r.n(F),q=r(55048),W=r.n(q),X=r(93780);function H(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function V(t){return(V="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var G=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Y=["points","pathLength"],K={svg:["viewBox","children"],polygon:Y,polyline:Y},Z=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],J=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,d.isValidElement)(t)&&(r=t.props),!W()(r))return null;var n={};return Object.keys(r).forEach(function(t){Z.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},Q=function(t,e,r){if(!W()(t)||"object"!==V(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];Z.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n},tt=["children"],te=["children"];function tr(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var tn={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},to=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},ti=null,ta=null,tc=function t(e){if(e===ti&&Array.isArray(ta))return ta;var r=[];return d.Children.forEach(e,function(e){E()(e)||((0,X.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),ta=r,ti=e,r};function tu(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return to(t)}):[to(e)],tc(t).forEach(function(t){var e=j()(t,"type.displayName")||j()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function tl(t,e){var r=tu(t,e);return r&&r[0]}var ts=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!T(r)&&!(r<=0)&&!!T(n)&&!(n<=0)},tf=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tp=function(t,e,r,n){var o,i=null!=(o=null==K?void 0:K[n])?o:[];return e.startsWith("data-")||!$()(t)&&(n&&i.includes(e)||G.includes(e))||r&&Z.includes(e)},th=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,d.isValidElement)(t)&&(n=t.props),!W()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;tp(null==(i=n)?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},td=function t(e,r){if(e===r)return!0;var n=d.Children.count(e);if(n!==d.Children.count(r))return!1;if(0===n)return!0;if(1===n)return ty(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=e[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!t(i,a))return!1}else if(!ty(i,a))return!1}return!0},ty=function(t,e){if(E()(t)&&E()(e))return!0;if(!E()(t)&&!E()(e)){var r=t.props||{},n=r.children,o=tr(r,tt),i=e.props||{},a=i.children,c=tr(i,te);if(n&&a)return H(o,c)&&td(n,a);if(!n&&!a)return H(o,c)}return!1},tv=function(t,e){var r=[],n={};return tc(t).forEach(function(t,o){var i;if((i=t)&&i.type&&g()(i.type)&&tf.indexOf(i.type)>=0)r.push(t);else if(t){var a=to(t.type),c=e[a]||{},u=c.handler,l=c.once;if(u&&(!l||!n[a])){var s=u(t,a,o);r.push(s),n[a]=!0}}}),r},tm=function(t){var e=t&&t.type;return e&&tn[e]?tn[e]:null};function tb(t){return(tb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tx(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tg(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=tb(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tb(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tb(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tg(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tw=(0,d.forwardRef)(function(t,e){var r,n=t.aspect,o=t.initialDimension,i=void 0===o?{width:-1,height:-1}:o,a=t.width,c=void 0===a?"100%":a,u=t.height,l=void 0===u?"100%":u,s=t.minWidth,f=void 0===s?0:s,p=t.minHeight,v=t.maxHeight,b=t.children,g=t.debounce,x=void 0===g?0:g,O=t.id,w=t.className,j=t.onResize,S=t.style,A=(0,d.useRef)(null),P=(0,d.useRef)();P.current=j,(0,d.useImperativeHandle)(e,function(){return Object.defineProperty(A.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),A.current},configurable:!0})});var E=function(t){if(Array.isArray(t))return t}(r=(0,d.useState)({containerWidth:i.width,containerHeight:i.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return tO(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tO(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),k=E[0],T=E[1],_=(0,d.useCallback)(function(t,e){T(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,d.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;_(n,o),null==(e=P.current)||e.call(P,n,o)};x>0&&(t=m()(t,x,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=A.current.getBoundingClientRect();return _(r.width,r.height),e.observe(A.current),function(){e.disconnect()}},[_,x]);var C=(0,d.useMemo)(function(){var t=k.containerWidth,e=k.containerHeight;if(t<0||e<0)return null;U(M(c)||M(l),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",c,l),U(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=M(c)?t:c,o=M(l)?e:l;n&&n>0&&(r?o=r/n:o&&(r=o*n),v&&o>v&&(o=v)),U(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,c,l,f,p,n);var i=!Array.isArray(b)&&to(b.type).endsWith("Chart");return y().Children.map(b,function(t){return y().isValidElement(t)?(0,d.cloneElement)(t,tx({width:r,height:o},i?{style:tx({height:"100%",width:"100%",maxHeight:o,maxWidth:r},t.props.style)}:{})):t})},[n,b,l,v,p,f,k,c]);return y().createElement("div",{id:O?"".concat(O):void 0,className:(0,h.A)("recharts-responsive-container",w),style:tx(tx({},void 0===S?{}:S),{},{width:c,height:l,minWidth:f,minHeight:p,maxHeight:v}),ref:A},C)}),tj=r(34990),tS=r.n(tj),tA=r(85938),tP=r.n(tA);function tE(t,e){if(!t)throw Error("Invariant failed")}var tk=["children","width","height","viewBox","className","style","title","desc"];function tM(){return(tM=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tT(t){var e=t.children,r=t.width,n=t.height,o=t.viewBox,i=t.className,a=t.style,c=t.title,u=t.desc,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tk),s=o||{width:r,height:n,x:0,y:0},f=(0,h.A)("recharts-surface",i);return y().createElement("svg",tM({},th(l,!0,"svg"),{className:f,width:r,height:n,style:a,viewBox:"".concat(s.x," ").concat(s.y," ").concat(s.width," ").concat(s.height)}),y().createElement("title",null,c),y().createElement("desc",null,u),e)}var t_=["children","className"];function tC(){return(tC=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tD=y().forwardRef(function(t,e){var r=t.children,n=t.className,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,t_),i=(0,h.A)("recharts-layer",n);return y().createElement("g",tC({className:i},th(o,!0),{ref:e}),r)});function tI(t){return(tI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tN(){return(tN=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tB(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tR(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tL(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tR(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=tI(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tI(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tR(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tz(t){return Array.isArray(t)&&_(t[0])&&_(t[1])?t.join(" ~ "):t}var tU=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,o=t.itemStyle,i=void 0===o?{}:o,a=t.labelStyle,c=t.payload,u=t.formatter,l=t.itemSorter,s=t.wrapperClassName,f=t.labelClassName,p=t.label,d=t.labelFormatter,v=t.accessibilityLayer,m=tL({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),b=tL({margin:0},void 0===a?{}:a),g=!E()(p),x=g?p:"",O=(0,h.A)("recharts-default-tooltip",s),w=(0,h.A)("recharts-tooltip-label",f);return g&&d&&null!=c&&(x=d(p,c)),y().createElement("div",tN({className:O,style:m},void 0!==v&&v?{role:"status","aria-live":"assertive"}:{}),y().createElement("p",{className:w,style:b},y().isValidElement(x)?x:"".concat(x)),function(){if(c&&c.length){var t=(l?tP()(c,l):c).map(function(t,e){if("none"===t.type)return null;var n=tL({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},i),o=t.formatter||u||tz,a=t.value,l=t.name,s=a,f=l;if(o&&null!=s&&null!=f){var p=o(a,l,t,e,c);if(Array.isArray(p)){var h=function(t){if(Array.isArray(t))return t}(p)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(p,2)||function(t,e){if(t){if("string"==typeof t)return tB(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tB(t,e)}}(p,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();s=h[0],f=h[1]}else s=p}return y().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},_(f)?y().createElement("span",{className:"recharts-tooltip-item-name"},f):null,_(f)?y().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,y().createElement("span",{className:"recharts-tooltip-item-value"},s),y().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return y().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function tF(t){return(tF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t$(t,e,r){var n;return(n=function(t,e){if("object"!=tF(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==tF(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var tq="recharts-tooltip-wrapper",tW={visibility:"hidden"};function tX(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(i&&T(i[n]))return i[n];var s=r[n]-c-o,f=r[n]+o;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function tH(t){return(tH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tV(Object(r),!0).forEach(function(e){tJ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tY(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tY=function(){return!!t})()}function tK(t){return(tK=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tZ(t,e){return(tZ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tJ(t,e,r){return(e=tQ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tQ(t){var e=function(t,e){if("object"!=tH(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tH(e)?e:e+""}var t0=function(t){var e;function r(){var t,e,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r,n=[].concat(i),e=tK(e),tJ(t=function(t,e){if(e&&("object"===tH(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,tY()?Reflect.construct(e,n||[],tK(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),tJ(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=t.props.coordinate)?void 0:n.x)?r:0,y:null!=(o=null==(i=t.props.coordinate)?void 0:i.y)?o:0}})}}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&tZ(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,o,i,a,c,u,l,s,f,p,d,v,m,b,g,x,O=this,w=this.props,j=w.active,S=w.allowEscapeViewBox,A=w.animationDuration,P=w.animationEasing,E=w.children,k=w.coordinate,M=w.hasPayload,_=w.isAnimationActive,C=w.offset,D=w.position,I=w.reverseDirection,N=w.useTranslate3d,B=w.viewBox,R=w.wrapperStyle,L=(f=(t={allowEscapeViewBox:S,coordinate:k,offsetTopLeft:C,position:D,reverseDirection:I,tooltipBox:this.state.lastBoundingBox,useTranslate3d:N,viewBox:B}).allowEscapeViewBox,p=t.coordinate,d=t.offsetTopLeft,v=t.position,m=t.reverseDirection,b=t.tooltipBox,g=t.useTranslate3d,x=t.viewBox,b.height>0&&b.width>0&&p?(r=(e={translateX:l=tX({allowEscapeViewBox:f,coordinate:p,key:"x",offsetTopLeft:d,position:v,reverseDirection:m,tooltipDimension:b.width,viewBox:x,viewBoxDimension:x.width}),translateY:s=tX({allowEscapeViewBox:f,coordinate:p,key:"y",offsetTopLeft:d,position:v,reverseDirection:m,tooltipDimension:b.height,viewBox:x,viewBoxDimension:x.height}),useTranslate3d:g}).translateX,n=e.translateY,u={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):u=tW,{cssProperties:u,cssClasses:(i=(o={translateX:l,translateY:s,coordinate:p}).coordinate,a=o.translateX,c=o.translateY,(0,h.A)(tq,t$(t$(t$(t$({},"".concat(tq,"-right"),T(a)&&i&&T(i.x)&&a>=i.x),"".concat(tq,"-left"),T(a)&&i&&T(i.x)&&a<i.x),"".concat(tq,"-bottom"),T(c)&&i&&T(i.y)&&c>=i.y),"".concat(tq,"-top"),T(c)&&i&&T(i.y)&&c<i.y)))}),z=L.cssClasses,U=L.cssProperties,F=tG(tG({transition:_&&j?"transform ".concat(A,"ms ").concat(P):void 0},U),{},{pointerEvents:"none",visibility:!this.state.dismissed&&j&&M?"visible":"hidden",position:"absolute",top:0,left:0},R);return y().createElement("div",{tabIndex:-1,className:z,style:F,ref:function(t){O.wrapperNode=t}},E)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tQ(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(d.PureComponent),t1={isSsr:!0,get:function(t){return t1[t]},set:function(t,e){if("string"==typeof t)t1[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){t1[e]=t[e]})}}},t2=r(36315),t5=r.n(t2);function t3(t,e,r){return!0===e?t5()(t,r):$()(e)?t5()(t,e):t}function t4(t){return(t4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t8(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t6(Object(r),!0).forEach(function(e){ee(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t7(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(t7=function(){return!!t})()}function t9(t){return(t9=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function et(t,e){return(et=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ee(t,e,r){return(e=er(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function er(t){var e=function(t,e){if("object"!=t4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t4(e)?e:e+""}function en(t){return t.dataKey}var eo=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=t9(t),function(t,e){if(e&&("object"===t4(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,t7()?Reflect.construct(t,e||[],t9(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&et(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,a=r.animationEasing,c=r.content,u=r.coordinate,l=r.filterNull,s=r.isAnimationActive,f=r.offset,p=r.payload,h=r.payloadUniqBy,d=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=p?p:[];l&&x.length&&(x=t3(p.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),h,en));var O=x.length>0;return y().createElement(t0,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:s,active:n,coordinate:u,hasPayload:O,offset:f,position:d,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(t=t8(t8({},this.props),{},{payload:x}),y().isValidElement(c)?y().cloneElement(c,t):"function"==typeof c?y().createElement(c,t):y().createElement(tU,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,er(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(d.PureComponent);ee(eo,"displayName","Tooltip"),ee(eo,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!t1.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var ei=r(69433),ea=r.n(ei);let ec=Math.cos,eu=Math.sin,el=Math.sqrt,es=Math.PI,ef=2*es,ep={draw(t,e){let r=el(e/es);t.moveTo(r,0),t.arc(0,0,r,0,ef)}},eh=el(1/3),ed=2*eh,ey=eu(es/10)/eu(7*es/10),ev=eu(ef/10)*ey,em=-ec(ef/10)*ey,eb=el(3),eg=el(3)/2,ex=1/el(12),eO=(ex/2+1)*3;function ew(t){return function(){return t}}let ej=Math.PI,eS=2*ej,eA=eS-1e-6;function eP(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class eE{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?eP:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return eP;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,n,o){if(t*=1,e*=1,r*=1,n*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-t,u=n-e,l=i-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6)if(Math.abs(s*c-u*l)>1e-6&&o){let p=r-i,h=n-a,d=c*c+u*u,y=Math.sqrt(d),v=Math.sqrt(f),m=o*Math.tan((ej-Math.acos((d+f-(p*p+h*h))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*l},${e+b*s}`,this._append`A${o},${o},0,0,${+(s*p>l*h)},${this._x1=t+g*c},${this._y1=e+g*u}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,n,o,i){if(t*=1,e*=1,r*=1,i=!!i,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%eS+eS),f>eA?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=ej)},${s},${this._x1=t+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function ek(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new eE(e)}function eM(t){return(eM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eE.prototype,el(3),el(3);var eT=["type","size","sizeType"];function e_(){return(e_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eC(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eD(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eC(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=eM(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eM(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eC(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var eI={symbolCircle:ep,symbolCross:{draw(t,e){let r=el(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=el(e/ed),n=r*eh;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=el(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=el(.8908130915292852*e),n=ev*r,o=em*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=ef*e/5,a=ec(i),c=eu(i);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*o,c*n+a*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-el(e/(3*eb));t.moveTo(0,2*r),t.lineTo(-eb*r,-r),t.lineTo(eb*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=el(e/eO),n=r/2,o=r*ex,i=r*ex+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-eg*o,eg*n+-.5*o),t.lineTo(-.5*n-eg*i,eg*n+-.5*i),t.lineTo(-.5*a-eg*i,eg*a+-.5*i),t.lineTo(-.5*n+eg*o,-.5*o-eg*n),t.lineTo(-.5*n+eg*i,-.5*i-eg*n),t.lineTo(-.5*a+eg*i,-.5*i-eg*a),t.closePath()}}},eN=Math.PI/180,eB=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*eN;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},eR=function(t){var e,r=t.type,n=void 0===r?"circle":r,o=t.size,i=void 0===o?64:o,a=t.sizeType,c=void 0===a?"area":a,u=eD(eD({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,eT)),{},{type:n,size:i,sizeType:c}),l=u.className,s=u.cx,f=u.cy,p=th(u,!0);return s===+s&&f===+f&&i===+i?y().createElement("path",e_({},p,{className:(0,h.A)("recharts-symbols",l),transform:"translate(".concat(s,", ").concat(f,")"),d:(e=eI["symbol".concat(ea()(n))]||ep,(function(t,e){let r=null,n=ek(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:ew(t||ep),e="function"==typeof e?e:ew(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:ew(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:ew(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(eB(i,c,n))())})):null};function eL(t){return(eL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ez(){return(ez=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eU(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}eR.registerSymbol=function(t,e){eI["symbol".concat(ea()(t))]=e};function eF(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eF=function(){return!!t})()}function e$(t){return(e$=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eq(t,e){return(eq=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eW(t,e,r){return(e=eX(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eX(t){var e=function(t,e){if("object"!=eL(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eL(e)?e:e+""}var eH=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=e$(t),function(t,e){if(e&&("object"===eL(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eF()?Reflect.construct(t,e||[],e$(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&eq(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,o=t.inactive?e:t.color;if("plainline"===t.type)return y().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return y().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return y().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(y().isValidElement(t.legendIcon)){var i=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eU(Object(r),!0).forEach(function(e){eW(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eU(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete i.legendIcon,y().cloneElement(t.legendIcon,i)}return y().createElement(eR,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,o=e.layout,i=e.formatter,a=e.inactiveColor,c={x:0,y:0,width:32,height:32},u={display:"horizontal"===o?"inline-block":"block",marginRight:10},l={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var o=e.formatter||i,s=(0,h.A)(eW(eW({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var f=$()(e.value)?null:e.value;U(!$()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var p=e.inactive?a:e.color;return y().createElement("li",ez({className:s,style:u,key:"legend-item-".concat(r)},Q(t.props,e,r)),y().createElement(tT,{width:n,height:n,viewBox:c,style:l},t.renderIcon(e)),y().createElement("span",{className:"recharts-legend-item-text",style:{color:p}},o?o(f,e,r):f))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?y().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eX(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(d.PureComponent);function eV(t){return(eV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eW(eH,"displayName","Legend"),eW(eH,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var eG=["ref"];function eY(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eY(Object(r),!0).forEach(function(e){e1(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eY(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function eZ(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e2(n.key),n)}}function eJ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eJ=function(){return!!t})()}function eQ(t){return(eQ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function e0(t,e){return(e0=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function e1(t,e,r){return(e=e2(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e2(t){var e=function(t,e){if("object"!=eV(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eV(e)?e:e+""}function e5(t){return t.value}var e3=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=eQ(e),e1(t=function(t,e){if(e&&("object"===eV(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eJ()?Reflect.construct(e,r||[],eQ(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&e0(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?eK({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),eK(eK({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,o=e.height,i=e.wrapperStyle,a=e.payloadUniqBy,c=e.payload,u=eK(eK({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return y().createElement("div",{className:"recharts-legend-wrapper",style:u,ref:function(e){t.wrapperNode=e}},function(t,e){if(y().isValidElement(t))return y().cloneElement(t,e);if("function"==typeof t)return y().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,eG);return y().createElement(eH,r)}(r,eK(eK({},this.props),{},{payload:t3(c,a,e5)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=eK(eK({},this.defaultProps),t.props).layout;return"vertical"===r&&T(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&eZ(n.prototype,e),r&&eZ(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(d.PureComponent);function e4(){return(e4=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}e1(e3,"displayName","Legend"),e1(e3,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var e6=function(t){var e=t.cx,r=t.cy,n=t.r,o=t.className,i=(0,h.A)("recharts-dot",o);return e===+e&&r===+r&&n===+n?d.createElement("circle",e4({},th(t,!1),J(t),{className:i,cx:e,cy:r,r:n})):null},e8=r(87955),e7=r.n(e8),e9=Object.getOwnPropertyNames,rt=Object.getOwnPropertySymbols,re=Object.prototype.hasOwnProperty;function rr(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function rn(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function ro(t){return e9(t).concat(rt(t))}var ri=Object.hasOwn||function(t,e){return re.call(t,e)};function ra(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var rc=Object.getOwnPropertyDescriptor,ru=Object.keys;function rl(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function rs(t,e){return ra(t.getTime(),e.getTime())}function rf(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rp(t,e){return t===e}function rh(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,h=o.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function rd(t,e,r){var n=ru(t),o=n.length;if(ru(e).length!==o)return!1;for(;o-- >0;)if(!rO(t,e,r,n[o]))return!1;return!0}function ry(t,e,r){var n,o,i,a=ro(t),c=a.length;if(ro(e).length!==c)return!1;for(;c-- >0;)if(!rO(t,e,r,n=a[c])||(o=rc(t,n),i=rc(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function rv(t,e){return ra(t.valueOf(),e.valueOf())}function rm(t,e){return t.source===e.source&&t.flags===e.flags}function rb(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(o=u.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function rg(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function rx(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rO(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||ri(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rw=Array.isArray,rj="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rS=Object.assign,rA=Object.prototype.toString.call.bind(Object.prototype.toString),rP=rE();function rE(t){void 0===t&&(t={});var e,r,n,o,i,a,c,u,l,s,f,p,h,d=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?ry:rl,areDatesEqual:rs,areErrorsEqual:rf,areFunctionsEqual:rp,areMapsEqual:n?rr(rh,ry):rh,areNumbersEqual:ra,areObjectsEqual:n?ry:rd,arePrimitiveWrappersEqual:rv,areRegExpsEqual:rm,areSetsEqual:n?rr(rb,ry):rb,areTypedArraysEqual:n?ry:rg,areUrlsEqual:rx};if(r&&(o=rS({},o,r(o))),e){var i=rn(o.areArraysEqual),a=rn(o.areMapsEqual),c=rn(o.areObjectsEqual),u=rn(o.areSetsEqual);o=rS({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,l=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,h=e.areUrlsEqual,function(t,e,d){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,d):"function"===y&&i(t,e,d);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return u(t,e,d);if(rw(t))return r(t,e,d);if(null!=rj&&rj(t))return p(t,e,d);if(v===Date)return n(t,e,d);if(v===RegExp)return s(t,e,d);if(v===Map)return a(t,e,d);if(v===Set)return f(t,e,d);var m=rA(t);return"[object Date]"===m?n(t,e,d):"[object RegExp]"===m?s(t,e,d):"[object Map]"===m?a(t,e,d):"[object Set]"===m?f(t,e,d):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,d):"[object URL]"===m?h(t,e,d):"[object Error]"===m?o(t,e,d):"[object Arguments]"===m?u(t,e,d):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(t,e,d)}),g=y?y(b):function(t,e,r,n,o,i,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:o,meta:c.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==d&&d,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rk(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function rM(t){return(rM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rT(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function r_(t){return(r_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rC(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rD(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rC(Object(r),!0).forEach(function(e){rI(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rC(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rI(t,e,r){var n;return(n=function(t,e){if("object"!==r_(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===r_(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}rE({strict:!0}),rE({circular:!0}),rE({circular:!0,strict:!0}),rE({createInternalComparator:function(){return ra}}),rE({strict:!0,createInternalComparator:function(){return ra}}),rE({circular:!0,createInternalComparator:function(){return ra}}),rE({circular:!0,createInternalComparator:function(){return ra},strict:!0});var rN=function(t){return t},rB=function(t,e){return Object.keys(e).reduce(function(r,n){return rD(rD({},r),{},rI({},n,t(n,e[n])))},{})},rR=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},rL=function(t,e,r,n,o,i,a,c){};function rz(t,e){if(t){if("string"==typeof t)return rU(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rU(t,e)}}function rU(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rF=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},r$=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},rq=function(t,e){return function(r){return r$(rF(t,e),r)}},rW=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,c=1,u=1;break;case"ease":i=.25,a=.1,c=.25,u=1;break;case"ease-in":i=.42,a=0,c=1,u=1;break;case"ease-out":i=.42,a=0,c=.58,u=1;break;case"ease-in-out":i=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(s,4)||rz(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],c=f[2],u=f[3]}else rL(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}rL([i,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=rq(i,c),h=rq(a,u),d=(t=i,e=c,function(r){var n;return r$([].concat(function(t){if(Array.isArray(t))return rU(t)}(n=rF(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||rz(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=d(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return h(r)};return y.isStepper=!1,y},rX=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(i)?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c},rH=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rW(n);case"spring":return rX();default:if("cubic-bezier"===n.split("(")[0])return rW(n);rL(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(rL(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function rV(t){return(rV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rG(t){return function(t){if(Array.isArray(t))return rQ(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||rJ(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rY(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rY(Object(r),!0).forEach(function(e){rZ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rY(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rZ(t,e,r){var n;return(n=function(t,e){if("object"!==rV(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rV(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rJ(t,e){if(t){if("string"==typeof t)return rQ(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rQ(t,e)}}function rQ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var r0=function(t,e,r){return t+(e-t)*r},r1=function(t){return t.from!==t.to},r2=function t(e,r,n){var o=rB(function(t,r){if(r1(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(n,2)||rJ(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return rK(rK({},r),{},{from:i,velocity:a})}return r},r);return n<1?rB(function(t,e){return r1(e)?rK(rK({},e),{},{velocity:r0(e.velocity,o[t].velocity,n),from:r0(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};let r5=function(t,e,r,n,o){var i,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return rK(rK({},r),{},rZ({},n,[t[n],e[n]]))},{}),l=c.reduce(function(r,n){return rK(rK({},r),{},rZ({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;l=r2(r,l,a),o(rK(rK(rK({},t),e),rB(function(t,e){return e.from},l))),i=n,Object.values(l).filter(r1).length&&(s=requestAnimationFrame(f))}:function(i){a||(a=i);var c=(i-a)/n,l=rB(function(t,e){return r0.apply(void 0,rG(e).concat([r(c)]))},u);if(o(rK(rK(rK({},t),e),l)),c<1)s=requestAnimationFrame(f);else{var p=rB(function(t,e){return r0.apply(void 0,rG(e).concat([r(1)]))},u);o(rK(rK(rK({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function r3(t){return(r3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var r4=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function r6(t){return function(t){if(Array.isArray(t))return r8(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return r8(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r8(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r8(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function r7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?r7(Object(r),!0).forEach(function(e){nt(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):r7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nt(t,e,r){return(e=ne(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ne(t){var e=function(t,e){if("object"!==r3(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===r3(e)?e:String(e)}function nr(t,e){return(nr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nn(t,e){if(e&&("object"===r3(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return no(t)}function no(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function ni(t){return(ni=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var na=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");o.prototype=Object.create(t&&t.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),t&&nr(o,t);var e,r,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=ni(o);return t=e?Reflect.construct(r,arguments,ni(this).constructor):r.apply(this,arguments),nn(this,t)});function o(t,e){if(!(this instanceof o))throw TypeError("Cannot call a class as a function");var r=n.call(this,t,e),i=r.props,a=i.isActive,c=i.attributeName,u=i.from,l=i.to,s=i.steps,f=i.children,p=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(no(r)),r.changeStyle=r.changeStyle.bind(no(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),nn(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},nn(r);r.state={style:c?nt({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:o?nt({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(l);return}if(!rP(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?c:t.to;if(this.state&&u){var p={style:o?nt({},o,f):f};(o&&u[o]!==f||!o&&u!==f)&&this.setState(p)}this.runAnimation(r9(r9({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=r5(r,n,rH(i),o,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},o,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration;return this.manager.start([o].concat(r6(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(r6(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var h=rR(p,i,c),d=r9(r9(r9({},f.style),u),{},{transition:h});return[].concat(r6(t),[d,i,s]).filter(rN)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=(r=function(){return null},n=!1,o=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var o=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return rT(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rT(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);return"number"==typeof i?void rk(t.bind(null,a),i):(t(i),void rk(t.bind(null,a)))}"object"===rM(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,o(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}}));var e,r,n,o,i=t.begin,a=t.duration,c=t.attributeName,u=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,h=t.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof h||"spring"===l)return void this.runJSAnimation(t);if(p.length>1)return void this.runStepAnimation(t);var y=c?nt({},c,u):u,v=rR(Object.keys(y),a,l);d.start([s,i,r9(r9({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),o=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,r4)),i=d.Children.count(e),a=this.state.style;if("function"==typeof e)return e(a);if(!n||0===i||r<=0)return e;var c=function(t){var e=t.props,r=e.style,n=e.className;return(0,d.cloneElement)(t,r9(r9({},o),{},{style:r9(r9({},void 0===r?{}:r),a),className:n}))};return 1===i?c(d.Children.only(e)):y().createElement("div",null,d.Children.map(e,function(t){return c(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ne(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(d.PureComponent);function nc(t){return(nc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nu(){return(nu=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nl(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ns(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ns(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=nc(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nc(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nc(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ns(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}na.displayName="Animate",na.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},na.propTypes={from:e7().oneOfType([e7().object,e7().string]),to:e7().oneOfType([e7().object,e7().string]),attributeName:e7().string,duration:e7().number,begin:e7().number,easing:e7().oneOfType([e7().string,e7().func]),steps:e7().arrayOf(e7().shape({duration:e7().number.isRequired,style:e7().object.isRequired,easing:e7().oneOfType([e7().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),e7().func]),properties:e7().arrayOf("string"),onAnimationEnd:e7().func})),children:e7().oneOfType([e7().node,e7().func]),isActive:e7().bool,canBegin:e7().bool,onAnimationEnd:e7().func,shouldReAnimate:e7().bool,onAnimationStart:e7().func,onAnimationReStart:e7().func};var np=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},nh=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+c),f=Math.max(i,i+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},nd={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},ny=function(t){var e,r=nf(nf({},nd),t),n=(0,d.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,d.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return nl(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nl(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];(0,d.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&a(t)}catch(t){}},[]);var c=r.x,u=r.y,l=r.width,s=r.height,f=r.radius,p=r.className,v=r.animationEasing,m=r.animationDuration,b=r.animationBegin,g=r.isAnimationActive,x=r.isUpdateAnimationActive;if(c!==+c||u!==+u||l!==+l||s!==+s||0===l||0===s)return null;var O=(0,h.A)("recharts-rectangle",p);return x?y().createElement(na,{canBegin:i>0,from:{width:l,height:s,x:c,y:u},to:{width:l,height:s,x:c,y:u},duration:m,animationEasing:v,isActive:x},function(t){var e=t.width,o=t.height,a=t.x,c=t.y;return y().createElement(na,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:b,duration:m,isActive:g,easing:v},y().createElement("path",nu({},th(r,!0),{className:O,d:np(a,c,e,o,f),ref:n})))}):y().createElement("path",nu({},th(r,!0),{className:O,d:np(c,u,l,s,f)}))};function nv(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function nm(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class nb extends Map{constructor(t,e=nx){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(ng(this,t))}has(t){return super.has(ng(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function ng({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function nx(t){return null!==t&&"object"==typeof t?t.valueOf():t}let nO=Symbol("implicit");function nw(){var t=new nb,e=[],r=[],n=nO;function o(o){let i=t.get(o);if(void 0===i){if(n!==nO)return n;t.set(o,i=e.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new nb,r))t.has(n)||t.set(n,e.push(n)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return nw(e,r).unknown(n)},nv.apply(o,arguments),o}function nj(){var t,e,r=nw().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<i,p=f?a:i,h=f?i:a;t=(h-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var d=(function(t,e,r){t*=1,e*=1,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=Array(o);++n<o;)i[n]=t+n*r;return i})(r).map(function(e){return p+t*e});return o(f?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([i,a]=t,i*=1,a*=1,f()):[i,a]},r.rangeRound=function(t){return[i,a]=t,i*=1,a*=1,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return nj(n(),[i,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},nv.apply(f(),arguments)}function nS(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(nj.apply(null,arguments).paddingInner(1))}function nA(t){return(nA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nE(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nP(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=nA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nA(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nA(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nk(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nM={widthCache:{},cacheCount:0},nT={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},n_="recharts_measurement_span",nC=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||t1.isSsr)return{width:0,height:0};var n=(Object.keys(e=nE({},r)).forEach(function(t){e[t]||delete e[t]}),e),o=JSON.stringify({text:t,copyStyle:n});if(nM.widthCache[o])return nM.widthCache[o];try{var i=document.getElementById(n_);i||((i=document.createElement("span")).setAttribute("id",n_),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=nE(nE({},nT),n);Object.assign(i.style,a),i.textContent="".concat(t);var c=i.getBoundingClientRect(),u={width:c.width,height:c.height};return nM.widthCache[o]=u,++nM.cacheCount>2e3&&(nM.cacheCount=0,nM.widthCache={}),u}catch(t){return{width:0,height:0}}};function nD(t){return(nD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nI(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nN(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nN(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nN(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nB(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=nD(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nD(e)?e:e+""}(n.key),n)}}var nR=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nL=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nz=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nU=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nF={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},n$=Object.keys(nF),nq=function(){var t,e;function r(t,e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||nz.test(e)||(this.num=NaN,this.unit=""),n$.includes(e)&&(this.num=t*nF[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=nI(null!=(e=nU.exec(t))?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&nB(r.prototype,t),e&&nB(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function nW(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=nI(null!=(r=nR.exec(e))?r:[],4),o=n[1],i=n[2],a=n[3],c=nq.parse(null!=o?o:""),u=nq.parse(null!=a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(nR,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=nI(null!=(s=nL.exec(e))?s:[],4),p=f[1],h=f[2],d=f[3],y=nq.parse(null!=p?p:""),v=nq.parse(null!=d?d:""),m="+"===h?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(nL,m.toString())}return e}var nX=/\(([^()]*)\)/;function nH(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=nI(nX.exec(e),2)[1];e=e.replace(nX,nW(r))}return e}(e),e=nW(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var nV=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],nG=["dx","dy","angle","className","breakAll"];function nY(){return(nY=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nK(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function nZ(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nJ(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nJ(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nJ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nQ=/[ \f\n\r\t\v\u2028\u2029]+/,n0=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];E()(e)||(o=r?e.toString().split(""):e.toString().split(nQ));var i=o.map(function(t){return{word:t,width:nC(t,n).width}}),a=r?0:nC("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(t){return null}},n1=function(t,e,r,n,o){var i,a=t.maxLines,c=t.children,u=t.style,l=t.breakAll,s=T(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||o||c.width+a+r<Number(n))?(c.words.push(i),c.width+=a+r):t.push({words:[i],width:a}),t},[])},p=f(e);if(!s)return p;for(var h=function(t){var e=f(n0({breakAll:l,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},d=0,y=c.length-1,v=0;d<=y&&v<=c.length-1;){var m=Math.floor((d+y)/2),b=nZ(h(m-1),2),g=b[0],x=b[1],O=nZ(h(m),1)[0];if(g||O||(d=m+1),g&&O&&(y=m-1),!g&&O){i=x;break}v++}return i||p},n2=function(t){return[{words:E()(t)?[]:t.toString().split(nQ)}]},n5=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!t1.isSsr){var c=n0({breakAll:i,children:n,style:o});if(!c)return n2(n);var u=c.wordsWithComputedWidth,l=c.spaceWidth;return n1({breakAll:i,children:n,maxLines:a,style:o},u,l,e,r)}return n2(n)},n3="#808080",n4=function(t){var e,r=t.x,n=void 0===r?0:r,o=t.y,i=void 0===o?0:o,a=t.lineHeight,c=void 0===a?"1em":a,u=t.capHeight,l=void 0===u?"0.71em":u,s=t.scaleToFit,f=void 0!==s&&s,p=t.textAnchor,v=t.verticalAnchor,m=t.fill,b=void 0===m?n3:m,g=nK(t,nV),x=(0,d.useMemo)(function(){return n5({breakAll:g.breakAll,children:g.children,maxLines:g.maxLines,scaleToFit:f,style:g.style,width:g.width})},[g.breakAll,g.children,g.maxLines,f,g.style,g.width]),O=g.dx,w=g.dy,j=g.angle,S=g.className,A=g.breakAll,P=nK(g,nG);if(!_(n)||!_(i))return null;var E=n+(T(O)?O:0),k=i+(T(w)?w:0);switch(void 0===v?"end":v){case"start":e=nH("calc(".concat(l,")"));break;case"middle":e=nH("calc(".concat((x.length-1)/2," * -").concat(c," + (").concat(l," / 2))"));break;default:e=nH("calc(".concat(x.length-1," * -").concat(c,")"))}var M=[];if(f){var C=x[0].width,D=g.width;M.push("scale(".concat((T(D)?D/C:1)/C,")"))}return j&&M.push("rotate(".concat(j,", ").concat(E,", ").concat(k,")")),M.length&&(P.transform=M.join(" ")),y().createElement("text",nY({},th(P,!0),{x:E,y:k,className:(0,h.A)("recharts-text",S),textAnchor:void 0===p?"start":p,fill:b.includes("url")?n3:b}),x.map(function(t,r){var n=t.words.join(A?"":" ");return y().createElement("tspan",{x:E,dy:0===r?e:c,key:"".concat(n,"-").concat(r)},n)}))};let n6=Math.sqrt(50),n8=Math.sqrt(10),n7=Math.sqrt(2);function n9(t,e,r){let n,o,i,a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),l=u>=n6?10:u>=n8?5:u>=n7?2:1;return(c<0?(n=Math.round(t*(i=Math.pow(10,-c)/l)),o=Math.round(e*i),n/i<t&&++n,o/i>e&&--o,i=-i):(n=Math.round(t/(i=Math.pow(10,c)*l)),o=Math.round(e/i),n*i<t&&++n,o*i>e&&--o),o<n&&.5<=r&&r<2)?n9(t,e,2*r):[n,o,i]}function ot(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[o,i,a]=n?n9(e,t,r):n9(t,e,r);if(!(i>=o))return[];let c=i-o+1,u=Array(c);if(n)if(a<0)for(let t=0;t<c;++t)u[t]=-((i-t)/a);else for(let t=0;t<c;++t)u[t]=(i-t)*a;else if(a<0)for(let t=0;t<c;++t)u[t]=-((o+t)/a);else for(let t=0;t<c;++t)u[t]=(o+t)*a;return u}function oe(t,e,r){return n9(t*=1,e*=1,r*=1)[2]}function or(t,e,r){e*=1,t*=1,r*=1;let n=e<t,o=n?oe(e,t,r):oe(t,e,r);return(n?-1:1)*(o<0?-(1/o):o)}function on(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function oo(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function oi(t){let e,r,n;function o(t,n,i=0,a=t.length){if(i<a){if(0!==e(n,n))return a;do{let e=i+a>>>1;0>r(t[e],n)?i=e+1:a=e}while(i<a)}return i}return 2!==t.length?(e=on,r=(e,r)=>on(t(e),r),n=(e,r)=>t(e)-r):(e=t===on||t===oo?t:oa,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){let a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>=r(t[e],n)?o=e+1:i=e}while(o<i)}return o}}}function oa(){return 0}function oc(t){return null===t?NaN:+t}let ou=oi(on),ol=ou.right;function os(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function of(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function op(){}ou.left,oi(oc).center;var oh="\\s*([+-]?\\d+)\\s*",od="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",oy="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",ov=/^#([0-9a-f]{3,8})$/,om=RegExp(`^rgb\\(${oh},${oh},${oh}\\)$`),ob=RegExp(`^rgb\\(${oy},${oy},${oy}\\)$`),og=RegExp(`^rgba\\(${oh},${oh},${oh},${od}\\)$`),ox=RegExp(`^rgba\\(${oy},${oy},${oy},${od}\\)$`),oO=RegExp(`^hsl\\(${od},${oy},${oy}\\)$`),ow=RegExp(`^hsla\\(${od},${oy},${oy},${od}\\)$`),oj={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function oS(){return this.rgb().formatHex()}function oA(){return this.rgb().formatRgb()}function oP(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=ov.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?oE(e):3===r?new oT(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?ok(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?ok(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=om.exec(t))?new oT(e[1],e[2],e[3],1):(e=ob.exec(t))?new oT(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=og.exec(t))?ok(e[1],e[2],e[3],e[4]):(e=ox.exec(t))?ok(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=oO.exec(t))?oB(e[1],e[2]/100,e[3]/100,1):(e=ow.exec(t))?oB(e[1],e[2]/100,e[3]/100,e[4]):oj.hasOwnProperty(t)?oE(oj[t]):"transparent"===t?new oT(NaN,NaN,NaN,0):null}function oE(t){return new oT(t>>16&255,t>>8&255,255&t,1)}function ok(t,e,r,n){return n<=0&&(t=e=r=NaN),new oT(t,e,r,n)}function oM(t,e,r,n){var o;return 1==arguments.length?((o=t)instanceof op||(o=oP(o)),o)?new oT((o=o.rgb()).r,o.g,o.b,o.opacity):new oT:new oT(t,e,r,null==n?1:n)}function oT(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function o_(){return`#${oN(this.r)}${oN(this.g)}${oN(this.b)}`}function oC(){let t=oD(this.opacity);return`${1===t?"rgb(":"rgba("}${oI(this.r)}, ${oI(this.g)}, ${oI(this.b)}${1===t?")":`, ${t})`}`}function oD(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function oI(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function oN(t){return((t=oI(t))<16?"0":"")+t.toString(16)}function oB(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new oL(t,e,r,n)}function oR(t){if(t instanceof oL)return new oL(t.h,t.s,t.l,t.opacity);if(t instanceof op||(t=oP(t)),!t)return new oL;if(t instanceof oL)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,c=i-o,u=(i+o)/2;return c?(a=e===i?(r-n)/c+(r<n)*6:r===i?(n-e)/c+2:(e-r)/c+4,c/=u<.5?i+o:2-i-o,a*=60):c=u>0&&u<1?0:a,new oL(a,c,u,t.opacity)}function oL(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function oz(t){return(t=(t||0)%360)<0?t+360:t}function oU(t){return Math.max(0,Math.min(1,t||0))}function oF(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function o$(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}os(op,oP,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:oS,formatHex:oS,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return oR(this).formatHsl()},formatRgb:oA,toString:oA}),os(oT,oM,of(op,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oT(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oT(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new oT(oI(this.r),oI(this.g),oI(this.b),oD(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:o_,formatHex:o_,formatHex8:function(){return`#${oN(this.r)}${oN(this.g)}${oN(this.b)}${oN((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:oC,toString:oC})),os(oL,function(t,e,r,n){return 1==arguments.length?oR(t):new oL(t,e,r,null==n?1:n)},of(op,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oL(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oL(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new oT(oF(t>=240?t-240:t+120,o,n),oF(t,o,n),oF(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new oL(oz(this.h),oU(this.s),oU(this.l),oD(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=oD(this.opacity);return`${1===t?"hsl(":"hsla("}${oz(this.h)}, ${100*oU(this.s)}%, ${100*oU(this.l)}%${1===t?")":`, ${t})`}`}}));let oq=t=>()=>t;function oW(t,e){var r,n,o=e-t;return o?(r=t,n=o,function(t){return r+t*n}):oq(isNaN(t)?e:t)}let oX=function t(e){var r,n=1==(r=+e)?oW:function(t,e){var n,o,i;return e-t?(n=t,o=e,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(t){return Math.pow(n+t*o,i)}):oq(isNaN(t)?e:t)};function o(t,e){var r=n((t=oM(t)).r,(e=oM(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=oW(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function oH(t){return function(e){var r,n,o=e.length,i=Array(o),a=Array(o),c=Array(o);for(r=0;r<o;++r)n=oM(e[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=t(i),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=c(t),n+""}}}oH(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,c=n<e-1?t[n+2]:2*i-o;return o$((r-n/e)*e,a,o,i,c)}}),oH(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return o$((r-n/e)*e,o,i,a,c)}});function oV(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var oG=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,oY=RegExp(oG.source,"g");function oK(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?oq(e):("number"===o?oV:"string"===o?(n=oP(e))?(e=n,oX):function(t,e){var r,n,o,i,a,c=oG.lastIndex=oY.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(o=oG.exec(t))&&(i=oY.exec(e));)(a=i.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(o=o[0])===(i=i[0])?l[u]?l[u]+=i:l[++u]=i:(l[++u]=null,s.push({i:u,x:oV(o,i)})),c=oY.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof oP?oX:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=oK(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},o={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=oK(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}:oV:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}})(t,e)}function oZ(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function oJ(t){return+t}var oQ=[0,1];function o0(t){return t}function o1(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function o2(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=o1(o,n),i=r(a,i)):(n=o1(n,o),i=r(i,a)),function(t){return i(n(t))}}function o5(t,e,r){var n=Math.min(t.length,e.length)-1,o=Array(n),i=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=o1(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=ol(t,e,1,n)-1;return i[r](o[r](e))}}function o3(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function o4(){var t,e,r,n,o,i,a=oQ,c=oQ,u=oK,l=o0;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==o0&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?o5:o2,o=i=null,f}function f(e){return null==e||isNaN(e*=1)?r:(o||(o=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(c,a.map(t),oV)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,oJ),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=oZ,s()},f.clamp=function(t){return arguments.length?(l=!!t||o0,s()):l!==o0},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function o6(){return o4()(o0,o0)}var o8=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function o7(t){var e;if(!(e=o8.exec(t)))throw Error("invalid format: "+t);return new o9({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function o9(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function it(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function ie(t){return(t=it(Math.abs(t)))?t[1]:NaN}function ir(t,e){var r=it(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}o7.prototype=o9.prototype,o9.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let io={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>ir(100*t,e),r:ir,s:function(t,e){var r=it(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(cI=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+it(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function ii(t){return t}var ia=Array.prototype.map,ic=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function iu(t,e,r,n){var o,i,a,c=or(t,e,r);switch((n=o7(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(ie(u)/3)))-ie(Math.abs(c))))||(n.precision=a),cR(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,ie(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=c)))-ie(o))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-ie(Math.abs(c))))||(n.precision=a-("%"===n.type)*2)}return cB(n)}function il(t){var e=t.domain;return t.ticks=function(t){var r=e();return ot(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return iu(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,c=i.length-1,u=i[a],l=i[c],s=10;for(l<u&&(o=u,u=l,l=o,o=a,a=c,c=o);s-- >0;){if((o=oe(u,l,r))===n)return i[a]=u,i[c]=l,e(i);if(o>0)u=Math.floor(u/o)*o,l=Math.ceil(l/o)*o;else if(o<0)u=Math.ceil(u*o)/o,l=Math.floor(l*o)/o;else break;n=o}return t},t}function is(){var t=o6();return t.copy=function(){return o3(t,is())},nv.apply(t,arguments),il(t)}function ip(t,e){t=t.slice();var r,n=0,o=t.length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function ih(t){return Math.log(t)}function id(t){return Math.exp(t)}function iy(t){return-Math.log(-t)}function iv(t){return-Math.exp(-t)}function im(t){return isFinite(t)?+("1e"+t):t<0?0:t}function ib(t){return(e,r)=>-t(-e,r)}function ig(t){let e,r,n=t(ih,id),o=n.domain,i=10;function a(){var a,c;return e=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=i)?im:c===Math.E?Math.exp:t=>Math.pow(c,t),o()[0]<0?(e=ib(e),r=ib(r),t(iy,iv)):t(ih,id),n}return n.base=function(t){return arguments.length?(i=+t,a()):i},n.domain=function(t){return arguments.length?(o(t),a()):o()},n.ticks=t=>{let n,a,c=o(),u=c[0],l=c[c.length-1],s=l<u;s&&([u,l]=[l,u]);let f=e(u),p=e(l),h=null==t?10:+t,d=[];if(!(i%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}}else for(;f<=p;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}2*d.length<h&&(d=ot(u,l,h))}else d=ot(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=o7(o)).precision||(o.trim=!0),o=cB(o)),t===1/0)return o;let a=Math.max(1,i*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*i<i-.5&&(n*=i),n<=a?o(t):""}},n.nice=()=>o(ip(o(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function ix(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function iO(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function iw(t){var e=1,r=t(ix(1),iO(e));return r.constant=function(r){return arguments.length?t(ix(e=+r),iO(e)):e},il(r)}function ij(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function iS(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function iA(t){return t<0?-t*t:t*t}function iP(t){var e=t(o0,o0),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(o0,o0):.5===r?t(iS,iA):t(ij(r),ij(1/r)):r},il(e)}function iE(){var t=iP(o4());return t.copy=function(){return o3(t,iE()).exponent(t.exponent())},nv.apply(t,arguments),t}function ik(){return iE.apply(null,arguments).exponent(.5)}function iM(t){return Math.sign(t)*t*t}function iT(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function i_(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}cB=(cN=function(t){var e,r,n,o=void 0===t.grouping||void 0===t.thousands?ii:(e=ia.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,c=e[0],u=0;o>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),i.push(t.substring(o-=c,o+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return i.reverse().join(r)}),i=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?ii:(n=ia.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),l=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=o7(t)).fill,r=t.align,n=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):io[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?i:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",O=io[b],w=/[defgprs%]/.test(b);function j(t){var i,a,l,p=g,j=x;if("c"===b)j=O(t)+j,t="";else{var S=(t*=1)<0||1/t<0;if(t=isNaN(t)?f:O(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==n&&(S=!1),p=(S?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===b?ic[8+cI/3]:"")+j+(S&&"("===n?")":""),w){for(i=-1,a=t.length;++i<a;)if(48>(l=t.charCodeAt(i))||l>57){j=(46===l?c+t.slice(i+1):t.slice(i))+j,t=t.slice(0,i);break}}}y&&!h&&(t=o(t,1/0));var A=p.length+t.length+j.length,P=A<d?Array(d-A+1).join(e):"";switch(y&&h&&(t=o(P+t,P.length?d-j.length:1/0),P=""),r){case"<":t=p+t+j+P;break;case"=":t=p+P+t+j;break;case"^":t=P.slice(0,A=P.length>>1)+p+t+j+P.slice(A);break;default:t=P+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=o7(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(ie(e)/3))),o=Math.pow(10,-n),i=ic[8+n/3];return function(t){return r(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,cR=cN.formatPrefix;function iC(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function iD(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let iI=new Date,iN=new Date;function iB(t,e,r,n){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{let e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{let a,c=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return c;do c.push(a=new Date(+r)),e(r,i),t(r);while(a<r&&r<n);return c},o.filter=r=>iB(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(o.count=(e,n)=>(iI.setTime(+e),iN.setTime(+n),t(iI),t(iN),Math.floor(r(iI,iN))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let iR=iB(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);iR.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?iB(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):iR:null,iR.range;let iL=iB(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());iL.range;let iz=iB(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());iz.range;let iU=iB(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());iU.range;let iF=iB(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());iF.range;let i$=iB(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());i$.range;let iq=iB(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);iq.range;let iW=iB(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);iW.range;let iX=iB(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function iH(t){return iB(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}iX.range;let iV=iH(0),iG=iH(1),iY=iH(2),iK=iH(3),iZ=iH(4),iJ=iH(5),iQ=iH(6);function i0(t){return iB(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}iV.range,iG.range,iY.range,iK.range,iZ.range,iJ.range,iQ.range;let i1=i0(0),i2=i0(1),i5=i0(2),i3=i0(3),i4=i0(4),i6=i0(5),i8=i0(6);i1.range,i2.range,i5.range,i3.range,i4.range,i6.range,i8.range;let i7=iB(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());i7.range;let i9=iB(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());i9.range;let at=iB(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());at.every=t=>isFinite(t=Math.floor(t))&&t>0?iB(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,at.range;let ae=iB(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function ar(t,e,r,n,o,i){let a=[[iL,1,1e3],[iL,5,5e3],[iL,15,15e3],[iL,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let o=Math.abs(r-e)/n,i=oi(([,,t])=>t).right(a,o);if(i===a.length)return t.every(or(e/31536e6,r/31536e6,n));if(0===i)return iR.every(Math.max(or(e,r,n),1));let[c,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let o=r&&"function"==typeof r.range?r:c(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},c]}ae.every=t=>isFinite(t=Math.floor(t))&&t>0?iB(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,ae.range;let[an,ao]=ar(ae,i9,i1,iX,i$,iU),[ai,aa]=ar(at,i7,iV,iq,iF,iz);function ac(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function au(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function al(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var as={"-":"",_:" ",0:"0"},af=/^\s*\d+/,ap=/^%/,ah=/[\\^$*+?|[\]().{}]/g;function ad(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?Array(r-i+1).join(e)+o:o)}function ay(t){return t.replace(ah,"\\$&")}function av(t){return RegExp("^(?:"+t.map(ay).join("|")+")","i")}function am(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function ab(t,e,r){var n=af.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function ag(t,e,r){var n=af.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function ax(t,e,r){var n=af.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function aO(t,e,r){var n=af.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function aw(t,e,r){var n=af.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function aj(t,e,r){var n=af.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function aS(t,e,r){var n=af.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aA(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aP(t,e,r){var n=af.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function aE(t,e,r){var n=af.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function ak(t,e,r){var n=af.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function aM(t,e,r){var n=af.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function aT(t,e,r){var n=af.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function a_(t,e,r){var n=af.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function aC(t,e,r){var n=af.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function aD(t,e,r){var n=af.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function aI(t,e,r){var n=af.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aN(t,e,r){var n=ap.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function aB(t,e,r){var n=af.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function aR(t,e,r){var n=af.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function aL(t,e){return ad(t.getDate(),e,2)}function az(t,e){return ad(t.getHours(),e,2)}function aU(t,e){return ad(t.getHours()%12||12,e,2)}function aF(t,e){return ad(1+iq.count(at(t),t),e,3)}function a$(t,e){return ad(t.getMilliseconds(),e,3)}function aq(t,e){return a$(t,e)+"000"}function aW(t,e){return ad(t.getMonth()+1,e,2)}function aX(t,e){return ad(t.getMinutes(),e,2)}function aH(t,e){return ad(t.getSeconds(),e,2)}function aV(t){var e=t.getDay();return 0===e?7:e}function aG(t,e){return ad(iV.count(at(t)-1,t),e,2)}function aY(t){var e=t.getDay();return e>=4||0===e?iZ(t):iZ.ceil(t)}function aK(t,e){return t=aY(t),ad(iZ.count(at(t),t)+(4===at(t).getDay()),e,2)}function aZ(t){return t.getDay()}function aJ(t,e){return ad(iG.count(at(t)-1,t),e,2)}function aQ(t,e){return ad(t.getFullYear()%100,e,2)}function a0(t,e){return ad((t=aY(t)).getFullYear()%100,e,2)}function a1(t,e){return ad(t.getFullYear()%1e4,e,4)}function a2(t,e){var r=t.getDay();return ad((t=r>=4||0===r?iZ(t):iZ.ceil(t)).getFullYear()%1e4,e,4)}function a5(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+ad(e/60|0,"0",2)+ad(e%60,"0",2)}function a3(t,e){return ad(t.getUTCDate(),e,2)}function a4(t,e){return ad(t.getUTCHours(),e,2)}function a6(t,e){return ad(t.getUTCHours()%12||12,e,2)}function a8(t,e){return ad(1+iW.count(ae(t),t),e,3)}function a7(t,e){return ad(t.getUTCMilliseconds(),e,3)}function a9(t,e){return a7(t,e)+"000"}function ct(t,e){return ad(t.getUTCMonth()+1,e,2)}function ce(t,e){return ad(t.getUTCMinutes(),e,2)}function cr(t,e){return ad(t.getUTCSeconds(),e,2)}function cn(t){var e=t.getUTCDay();return 0===e?7:e}function co(t,e){return ad(i1.count(ae(t)-1,t),e,2)}function ci(t){var e=t.getUTCDay();return e>=4||0===e?i4(t):i4.ceil(t)}function ca(t,e){return t=ci(t),ad(i4.count(ae(t),t)+(4===ae(t).getUTCDay()),e,2)}function cc(t){return t.getUTCDay()}function cu(t,e){return ad(i2.count(ae(t)-1,t),e,2)}function cl(t,e){return ad(t.getUTCFullYear()%100,e,2)}function cs(t,e){return ad((t=ci(t)).getUTCFullYear()%100,e,2)}function cf(t,e){return ad(t.getUTCFullYear()%1e4,e,4)}function cp(t,e){var r=t.getUTCDay();return ad((t=r>=4||0===r?i4(t):i4.ceil(t)).getUTCFullYear()%1e4,e,4)}function ch(){return"+0000"}function cd(){return"%"}function cy(t){return+t}function cv(t){return Math.floor(t/1e3)}function cm(t){return new Date(t)}function cb(t){return t instanceof Date?+t:+new Date(+t)}function cg(t,e,r,n,o,i,a,c,u,l){var s=o6(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function O(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,cb)):p().map(cm)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?O:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(ip(r,t)):s},s.copy=function(){return o3(s,cg(t,e,r,n,o,i,a,c,u,l))},s}function cx(){return nv.apply(cg(ai,aa,at,i7,iV,iq,iF,iz,iL,cz).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cO(){return nv.apply(cg(an,ao,ae,i9,i1,iW,i$,iU,iL,cU).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cw(){var t,e,r,n,o,i=0,a=1,c=o0,u=!1;function l(e){return null==e||isNaN(e*=1)?o:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(oK),l.rangeRound=s(oZ),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function cj(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function cS(){var t=iP(cw());return t.copy=function(){return cj(t,cS()).exponent(t.exponent())},nm.apply(t,arguments)}function cA(){return cS.apply(null,arguments).exponent(.5)}function cP(){var t,e,r,n,o,i,a,c=0,u=.5,l=1,s=1,f=o0,p=!1;function h(t){return isNaN(t*=1)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=oK);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=i(c*=1),e=i(u*=1),r=i(l*=1),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(oK),h.rangeRound=d(oZ),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function cE(){var t=iP(cP());return t.copy=function(){return cj(t,cE()).exponent(t.exponent())},nm.apply(t,arguments)}function ck(){return cE.apply(null,arguments).exponent(.5)}function cM(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],c=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function cT(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function c_(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function cC(t,e){return t[e]}function cD(t){let e=[];return e.key=t,e}cz=(cL=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=av(o),s=am(o),f=av(i),p=am(i),h=av(a),d=am(a),y=av(c),v=am(c),m=av(u),b=am(u),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:aL,e:aL,f:aq,g:a0,G:a2,H:az,I:aU,j:aF,L:a$,m:aW,M:aX,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:cy,s:cv,S:aH,u:aV,U:aG,V:aK,w:aZ,W:aJ,x:null,X:null,y:aQ,Y:a1,Z:a5,"%":cd},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:a3,e:a3,f:a9,g:cs,G:cp,H:a4,I:a6,j:a8,L:a7,m:ct,M:ce,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:cy,s:cv,S:cr,u:cn,U:co,V:ca,w:cc,W:cu,x:null,X:null,y:cl,Y:cf,Z:ch,"%":cd},O={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:ak,e:ak,f:aI,g:aS,G:aj,H:aT,I:aT,j:aM,L:aD,m:aE,M:a_,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:aP,Q:aB,s:aR,S:aC,u:ag,U:ax,V:aO,w:ab,W:aw,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:aS,Y:aj,Z:aA,"%":aN};function w(t,e){return function(r){var n,o,i,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(o=as[n=t.charAt(++c)])?n=t.charAt(++c):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,o,i=al(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=au(al(i.y,0,1))).getUTCDay())>4||0===o?i2.ceil(n):i2(n),n=iW.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=ac(al(i.y,0,1))).getDay())>4||0===o?iG.ceil(n):iG(n),n=iq.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?au(al(i.y,0,1)).getUTCDay():ac(al(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,au(i)):ac(i)}}function S(t,e,r,n){for(var o,i,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=O[(o=e.charAt(a++))in as?e.charAt(a++):o])||(n=i(t,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=w(r,g),g.X=w(n,g),g.c=w(e,g),x.x=w(r,x),x.X=w(n,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,cL.parse,cU=cL.utcFormat,cL.utcParse,Array.prototype.slice;var cI,cN,cB,cR,cL,cz,cU,cF,c$,cq=r(90453),cW=r.n(cq),cX=r(15883),cH=r.n(cX),cV=r(21592),cG=r.n(cV),cY=r(71967),cK=r.n(cY),cZ=!0,cJ="[DecimalError] ",cQ=cJ+"Invalid argument: ",c0=cJ+"Exponent out of range: ",c1=Math.floor,c2=Math.pow,c5=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,c3=c1(1286742750677284.5),c4={};function c6(t,e){var r,n,o,i,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),cZ?ua(e,f):e;if(u=t.d,l=e.d,a=t.e,o=e.e,u=u.slice(),i=a-o){for(i<0?(n=u,i=-i,c=l.length):(n=l,o=a,c=u.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=u.length)-(i=l.length)<0&&(i=c,n=l,l=u,u=n),r=0;i;)r=(u[--i]=u[i]+l[i]+r)/1e7|0,u[i]%=1e7;for(r&&(u.unshift(r),++o),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=o,cZ?ua(e,f):e}function c8(t,e,r){if(t!==~~t||t<e||t>r)throw Error(cQ+t)}function c7(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=un(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=un(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}c4.absoluteValue=c4.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},c4.comparedTo=c4.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},c4.decimalPlaces=c4.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},c4.dividedBy=c4.div=function(t){return c9(this,new this.constructor(t))},c4.dividedToIntegerBy=c4.idiv=function(t){var e=this.constructor;return ua(c9(this,new e(t),0,1),e.precision)},c4.equals=c4.eq=function(t){return!this.cmp(t)},c4.exponent=function(){return ue(this)},c4.greaterThan=c4.gt=function(t){return this.cmp(t)>0},c4.greaterThanOrEqualTo=c4.gte=function(t){return this.cmp(t)>=0},c4.isInteger=c4.isint=function(){return this.e>this.d.length-2},c4.isNegative=c4.isneg=function(){return this.s<0},c4.isPositive=c4.ispos=function(){return this.s>0},c4.isZero=function(){return 0===this.s},c4.lessThan=c4.lt=function(t){return 0>this.cmp(t)},c4.lessThanOrEqualTo=c4.lte=function(t){return 1>this.cmp(t)},c4.logarithm=c4.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(c$))throw Error(cJ+"NaN");if(this.s<1)throw Error(cJ+(this.s?"NaN":"-Infinity"));return this.eq(c$)?new r(0):(cZ=!1,e=c9(uo(this,o),uo(t,o),o),cZ=!0,ua(e,n))},c4.minus=c4.sub=function(t){return t=new this.constructor(t),this.s==t.s?uc(this,t):c6(this,(t.s=-t.s,t))},c4.modulo=c4.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(cJ+"NaN");return this.s?(cZ=!1,e=c9(this,t,0,1).times(t),cZ=!0,this.minus(e)):ua(new r(this),n)},c4.naturalExponential=c4.exp=function(){return ut(this)},c4.naturalLogarithm=c4.ln=function(){return uo(this)},c4.negated=c4.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},c4.plus=c4.add=function(t){return t=new this.constructor(t),this.s==t.s?c6(this,t):uc(this,(t.s=-t.s,t))},c4.precision=c4.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(cQ+t);if(e=ue(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},c4.squareRoot=c4.sqrt=function(){var t,e,r,n,o,i,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(cJ+"NaN")}for(t=ue(this),cZ=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=c7(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=c1((t+1)/2)-(t<0||t%2),n=new c(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(c9(this,i,a+2)).times(.5),c7(i.d).slice(0,a)===(e=c7(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(ua(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return cZ=!0,ua(n,r)},c4.times=c4.mul=function(t){var e,r,n,o,i,a,c,u,l,s=this.constructor,f=this.d,p=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(l=p.length)&&(i=f,f=p,p=i,a=u,u=l,l=a),i=[],n=a=u+l;n--;)i.push(0);for(n=l;--n>=0;){for(e=0,o=u+n;o>n;)c=i[o]+p[n]*f[o-n-1]+e,i[o--]=c%1e7|0,e=c/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,cZ?ua(t,s.precision):t},c4.toDecimalPlaces=c4.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(c8(t,0,1e9),void 0===e?e=n.rounding:c8(e,0,8),ua(r,t+ue(r)+1,e))},c4.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=uu(n,!0):(c8(t,0,1e9),void 0===e?e=o.rounding:c8(e,0,8),r=uu(n=ua(new o(n),t+1,e),!0,t+1)),r},c4.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?uu(this):(c8(t,0,1e9),void 0===e?e=o.rounding:c8(e,0,8),r=uu((n=ua(new o(this),t+ue(this)+1,e)).abs(),!1,t+ue(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},c4.toInteger=c4.toint=function(){var t=this.constructor;return ua(new t(this),ue(this)+1,t.rounding)},c4.toNumber=function(){return+this},c4.toPower=c4.pow=function(t){var e,r,n,o,i,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(c$);if(!(c=new u(c)).s){if(t.s<1)throw Error(cJ+"Infinity");return c}if(c.eq(c$))return c;if(n=u.precision,t.eq(c$))return ua(c,n);if(a=(e=t.e)>=(r=t.d.length-1),i=c.s,a){if((r=l<0?-l:l)<=0x1fffffffffffff){for(o=new u(c$),e=Math.ceil(n/7+4),cZ=!1;r%2&&ul((o=o.times(c)).d,e),0!==(r=c1(r/2));)ul((c=c.times(c)).d,e);return cZ=!0,t.s<0?new u(c$).div(o):ua(o,n)}}else if(i<0)throw Error(cJ+"NaN");return i=i<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,cZ=!1,o=t.times(uo(c,n+12)),cZ=!0,(o=ut(o)).s=i,o},c4.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=ue(o),n=uu(o,r<=i.toExpNeg||r>=i.toExpPos)):(c8(t,1,1e9),void 0===e?e=i.rounding:c8(e,0,8),r=ue(o=ua(new i(o),t,e)),n=uu(o,t<=r||r<=i.toExpNeg,t)),n},c4.toSignificantDigits=c4.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(c8(t,1,1e9),void 0===e?e=r.rounding:c8(e,0,8)),ua(new r(this),t,e)},c4.toString=c4.valueOf=c4.val=c4.toJSON=c4[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=ue(this),e=this.constructor;return uu(this,t<=e.toExpNeg||t>=e.toExpPos)};var c9=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,u,l,s,f,p,h,d,y,v,m,b,g,x,O,w,j,S,A=n.constructor,P=n.s==o.s?1:-1,E=n.d,k=o.d;if(!n.s)return new A(n);if(!o.s)throw Error(cJ+"Division by zero");for(l=0,u=n.e-o.e,j=k.length,O=E.length,d=(h=new A(P)).d=[];k[l]==(E[l]||0);)++l;if(k[l]>(E[l]||0)&&--u,(b=null==i?i=A.precision:a?i+(ue(n)-ue(o))+1:i)<0)return new A(0);if(b=b/7+2|0,l=0,1==j)for(s=0,k=k[0],b++;(l<O||s)&&b--;l++)g=1e7*s+(E[l]||0),d[l]=g/k|0,s=g%k|0;else{for((s=1e7/(k[0]+1)|0)>1&&(k=t(k,s),E=t(E,s),j=k.length,O=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(S=k.slice()).unshift(0),w=k[0],k[1]>=1e7/2&&++w;do s=0,(c=e(k,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/w|0)>1?(s>=1e7&&(s=1e7-1),p=(f=t(k,s)).length,v=y.length,1==(c=e(f,y,p,v))&&(s--,r(f,j<p?S:k,p))):(0==s&&(c=s=1),f=k.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(k,y,j,v))<1&&(s++,r(y,j<v?S:k,v))),v=y.length):0===c&&(s++,y=[0]),d[l++]=s,c&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1);while((x++<O||void 0!==y[0])&&b--)}return d[0]||d.shift(),h.e=u,ua(h,a?i+ue(h)+1:i)}}();function ut(t,e){var r,n,o,i,a,c=0,u=0,l=t.constructor,s=l.precision;if(ue(t)>16)throw Error(c0+ue(t));if(!t.s)return new l(c$);for(null==e?(cZ=!1,a=s):a=e,i=new l(.03125);t.abs().gte(.1);)t=t.times(i),u+=5;for(a+=Math.log(c2(2,u))/Math.LN10*2+5|0,r=n=o=new l(c$),l.precision=a;;){if(n=ua(n.times(t),a),r=r.times(++c),c7((i=o.plus(c9(n,r,a))).d).slice(0,a)===c7(o.d).slice(0,a)){for(;u--;)o=ua(o.times(o),a);return l.precision=s,null==e?(cZ=!0,ua(o,s)):o}o=i}}function ue(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function ur(t,e,r){if(e>t.LN10.sd())throw cZ=!0,r&&(t.precision=r),Error(cJ+"LN10 precision limit exceeded");return ua(new t(t.LN10),e)}function un(t){for(var e="";t--;)e+="0";return e}function uo(t,e){var r,n,o,i,a,c,u,l,s,f=1,p=t,h=p.d,d=p.constructor,y=d.precision;if(p.s<1)throw Error(cJ+(p.s?"NaN":"-Infinity"));if(p.eq(c$))return new d(0);if(null==e?(cZ=!1,l=y):l=e,p.eq(10))return null==e&&(cZ=!0),ur(d,l);if(d.precision=l+=10,n=(r=c7(h)).charAt(0),!(15e14>Math.abs(i=ue(p))))return u=ur(d,l+2,y).times(i+""),p=uo(new d(n+"."+r.slice(1)),l-10).plus(u),d.precision=y,null==e?(cZ=!0,ua(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=c7((p=p.times(t)).d)).charAt(0),f++;for(i=ue(p),n>1?(p=new d("0."+r),i++):p=new d(n+"."+r.slice(1)),c=a=p=c9(p.minus(c$),p.plus(c$),l),s=ua(p.times(p),l),o=3;;){if(a=ua(a.times(s),l),c7((u=c.plus(c9(a,new d(o),l))).d).slice(0,l)===c7(c.d).slice(0,l))return c=c.times(2),0!==i&&(c=c.plus(ur(d,l+2,y).times(i+""))),c=c9(c,new d(f),l),d.precision=y,null==e?(cZ=!0,ua(c,y)):c;c=u,o+=2}}function ui(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,t.e=c1((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),cZ&&(t.e>c3||t.e<-c3))throw Error(c0+r)}else t.s=0,t.e=0,t.d=[0];return t}function ua(t,e,r){var n,o,i,a,c,u,l,s,f=t.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(i=f.length))return t;for(a=1,l=i=f[s];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=l/(i=c2(10,a-o-1))%10|0,u=e<0||void 0!==f[s+1]||l%i,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?o>0?l/c2(10,a-o):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(i=ue(t),f.length=1,e=e-i-1,f[0]=c2(10,(7-e%7)%7),t.e=c1(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,i=1,s--):(f.length=s+1,i=c2(10,7-n),f[s]=o>0?(l/c2(10,a-o)%c2(10,o)|0)*i:0),u)for(;;)if(0==s){1e7==(f[0]+=i)&&(f[0]=1,++t.e);break}else{if(f[s]+=i,1e7!=f[s])break;f[s--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(cZ&&(t.e>c3||t.e<-c3))throw Error(c0+ue(t));return t}function uc(t,e){var r,n,o,i,a,c,u,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),cZ?ua(e,h):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(o=Math.max(Math.ceil(h/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((s=(o=u.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(u[o]!=f[o]){s=u[o]<f[o];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,o=f.length-c;o>0;--o)u[c++]=0;for(o=f.length;o>a;){if(u[--o]<f[o]){for(i=o;i&&0===u[--i];)u[i]=1e7-1;--u[i],u[o]+=1e7}u[o]-=f[o]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,cZ?ua(e,h):e):new p(0)}function uu(t,e,r){var n,o=ue(t),i=c7(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+un(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+un(-o-1)+i,r&&(n=r-a)>0&&(i+=un(n))):o>=a?(i+=un(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+un(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=un(n))),t.s<0?"-"+i:i}function ul(t,e){if(t.length>e)return t.length=e,!0}function us(t){if(!t||"object"!=typeof t)throw Error(cJ+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]]))if(c1(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(cQ+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(cQ+r+": "+n);return this}var cF=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(cQ+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return ui(this,t.toString())}if("string"!=typeof t)throw Error(cQ+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,c5.test(t))ui(this,t);else throw Error(cQ+t)}if(i.prototype=c4,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=us,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});c$=new cF(1);let uf=cF;function up(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var uh=function(t){return t},ud={},uy=function(t){return t===ud},uv=function(t){return function e(){return 0==arguments.length||1==arguments.length&&uy(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},um=function(t){return function t(e,r){return 1===e?r:uv(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==ud}).length;return a>=e?r.apply(void 0,o):t(e-a,uv(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return uy(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return up(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return up(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return up(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},ub=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},ug=um(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),ux=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return uh;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},uO=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},uw=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};um(function(t,e,r){var n=+t;return n+r*(e-n)}),um(function(t,e,r){var n=e-t;return(r-t)/(n=n||1/0)}),um(function(t,e,r){var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let uj={rangeStep:function(t,e,r){for(var n=new uf(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new uf(t).abs().log(10).toNumber())+1}};function uS(t){return function(t){if(Array.isArray(t))return uE(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||uP(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uA(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}}(t,e)||uP(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uP(t,e){if(t){if("string"==typeof t)return uE(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uE(t,e)}}function uE(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uk(t){var e=uA(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function uM(t,e,r){if(t.lte(0))return new uf(0);var n=uj.getDigitCount(t.toNumber()),o=new uf(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new uf(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new uf(Math.ceil(c))}function uT(t,e,r){var n=1,o=new uf(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new uf(10).pow(uj.getDigitCount(t)-1),o=new uf(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new uf(Math.floor(t)))}else 0===t?o=new uf(Math.floor((e-1)/2)):r||(o=new uf(Math.floor(t)));var a=Math.floor((e-1)/2);return ux(ug(function(t){return o.add(new uf(t-a).mul(n)).toNumber()}),ub)(0,e)}var u_=uw(function(t){var e=uA(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uA(uk([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(uS(ub(0,o-1).map(function(){return 1/0}))):[].concat(uS(ub(0,o-1).map(function(){return-1/0})),[l]);return r>n?uO(s):s}if(u===l)return uT(u,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new uf(0),tickMin:new uf(0),tickMax:new uf(0)};var c=uM(new uf(r).sub(e).div(n-1),o,a),u=Math.ceil((i=e<=0&&r>=0?new uf(0):(i=new uf(e).add(r).div(2)).sub(new uf(i).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new uf(r).sub(i).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:i.sub(new uf(u).mul(c)),tickMax:i.add(new uf(l).mul(c))})}(u,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=uj.rangeStep(h,d.add(new uf(.1).mul(p)),p);return r>n?uO(y):y});uw(function(t){var e=uA(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uA(uk([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return uT(u,o,i);var s=uM(new uf(l).sub(u).div(a-1),i,0),f=ux(ug(function(t){return new uf(u).add(new uf(t).mul(s)).toNumber()}),ub)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?uO(f):f});var uC=uw(function(t,e){var r=uA(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=uA(uk([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var l=Math.max(e,2),s=uM(new uf(u).sub(c).div(l-1),i,0),f=[].concat(uS(uj.rangeStep(new uf(c),new uf(u).sub(new uf(.99).mul(s)),s)),[u]);return n>o?uO(f):f}),uD=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function uI(t){return(uI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uN(){return(uN=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function uB(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uR(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(uR=function(){return!!t})()}function uL(t){return(uL=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function uz(t,e){return(uz=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uU(t,e,r){return(e=uF(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uF(t){var e=function(t,e){if("object"!=uI(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uI(e)?e:e+""}var u$=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=uL(t),function(t,e){if(e&&("object"===uI(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,uR()?Reflect.construct(t,e||[],uL(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&uz(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,o=t.dataKey,i=t.data,a=t.dataPointFormatter,c=t.xAxis,u=t.yAxis,l=th(function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,uD),!1);"x"===this.props.direction&&"number"!==c.type&&tE(!1);var s=i.map(function(t){var i,s,f=a(t,o),p=f.x,h=f.y,d=f.value,v=f.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return uB(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uB(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=b[0],s=b[1]}else i=s=v;if("vertical"===r){var g=c.scale,x=h+e,O=x+n,w=x-n,j=g(d-i),S=g(d+s);m.push({x1:S,y1:O,x2:S,y2:w}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:O,x2:j,y2:w})}else if("horizontal"===r){var A=u.scale,P=p+e,E=P-n,k=P+n,M=A(d-i),T=A(d+s);m.push({x1:E,y1:T,x2:k,y2:T}),m.push({x1:P,y1:M,x2:P,y2:T}),m.push({x1:E,y1:M,x2:k,y2:M})}return y().createElement(tD,uN({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},l),m.map(function(t){return y().createElement("line",uN({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return y().createElement(tD,{className:"recharts-errorBars"},s)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uF(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(y().Component);function uq(t){return(uq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uW(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uX(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uW(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=uq(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uq(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uW(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}uU(u$,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),uU(u$,"displayName","ErrorBar");var uH=function(t){var e,r=t.children,n=t.formattedGraphicalItems,o=t.legendWidth,i=t.legendContent,a=tl(r,e3);if(!a)return null;var c=e3.defaultProps,u=void 0!==c?uX(uX({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?uX(uX({},r),e.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:u.iconType||a||"square",color:u2(e),value:i||o,payload:n}}),uX(uX(uX({},u),e3.getWithHeight(a,o)),{},{payload:e,item:a})};function uV(t){return(uV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uG(t){return function(t){if(Array.isArray(t))return uY(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return uY(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uY(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uY(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uK(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uZ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uK(Object(r),!0).forEach(function(e){uJ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uK(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function uJ(t,e,r){var n;return(n=function(t,e){if("object"!=uV(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==uV(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uQ(t,e,r){return E()(t)||E()(e)?r:_(e)?j()(t,e,r):$()(e)?e(t):r}function u0(t,e,r,n){var o=cG()(t,function(t){return uQ(t,e)});if("number"===r){var i=o.filter(function(t){return T(t)||parseFloat(t)});return i.length?[cH()(i),cW()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!E()(t)}):o).map(function(t){return _(t)||t instanceof Date?t:""})}var u1=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!=(e=null==r?void 0:r.length)?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(k(s-l)!==k(f-s)){var h=[];if(k(f-s)===k(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},u2=function(t){var e,r,n=t.type.displayName,o=null!=(e=t.type)&&e.defaultProps?uZ(uZ({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},u5=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return to(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?uZ(uZ({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var O=E()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:E()(O)?void 0:I(O,r,0)})}}return i},u3=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=I(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/u,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=o&&(h-=(u-1)*l,l=0),h>=o&&p>0&&(f=!0,p*=.9,h=u*p);var d={offset:((o-h)/2|0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(uG(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=I(n,o,0,!0);o-2*y-(u-1)*l<=0&&(l=0);var v=(o-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(uG(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},u4=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=uH({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(c){var u=n||{},l=u.width,s=u.height,f=c.align,p=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===p)&&"center"!==f&&T(t[f]))return uZ(uZ({},t),{},uJ({},f,t[f]+(l||0)));if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==p&&T(t[p]))return uZ(uZ({},t),{},uJ({},p,t[p]+(s||0)))}return t},u6=function(t,e,r,n,o){var i=tu(e.props.children,u$).filter(function(t){var e;return e=t.props.direction,!!E()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(i&&i.length){var a=i.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=uQ(e,r);if(E()(n))return t;var o=Array.isArray(n)?[cH()(n),cW()(n)]:[n,n],i=a.reduce(function(t,r){var n=uQ(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},u8=function(t,e,r,n,o){var i=e.map(function(e){return u6(t,e,r,o,n)}).filter(function(t){return!E()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},u7=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&u6(t,e,i,n)||u0(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},u9=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},lt=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(r),a},le=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*k(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!O()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}})},lr=new WeakMap,ln=function(t,e){if("function"!=typeof e)return t;lr.has(t)||lr.set(t,new WeakMap);var r=lr.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},lo=function(t,e,r){var o=t.scale,i=t.type,a=t.layout,c=t.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:nj(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:is(),realScaleType:"linear"}:"category"===i&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:nS(),realScaleType:"point"}:"category"===i?{scale:nj(),realScaleType:"band"}:{scale:is(),realScaleType:"linear"};if(g()(o)){var u="scale".concat(ea()(o));return{scale:(n[u]||nS)(),realScaleType:n[u]?u:"point"}}return $()(o)?{scale:o}:{scale:nS(),realScaleType:"point"}},li=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}},la=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},lc=function(t,e){if(!e||2!==e.length||!T(e[0])||!T(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!T(t[0])||t[0]<r)&&(o[0]=r),(!T(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},lu={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=O()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}cM(t,e)}},none:cM,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}cM(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<o;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,u&&(i-=l/u)}r[a-1][1]+=r[a-1][0]=i,cM(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=O()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},ll=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=lu[r];return(function(){var t=ew([]),e=c_,r=cM,n=cC;function o(o){var i,a,c=Array.from(t.apply(this,arguments),cD),u=c.length,l=-1;for(let t of o)for(i=0,++l;i<u;++i)(c[i][l]=[0,+n(t,c[i].key,l,o)]).data=t;for(i=0,a=cT(e(c));i<u;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:ew(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:ew(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?c_:"function"==typeof t?t:ew(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?cM:t,o):r},o})().keys(n).value(function(t,e){return+uQ(t,e,0)}).order(c_).offset(o)(t)},ls=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!=(o=e.type)&&o.defaultProps?uZ(uZ({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(_(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[D("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return uZ(uZ({},t),{},uJ({},c,u))},{});return Object.keys(a).reduce(function(e,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,i){var a=c.stackGroups[i];return uZ(uZ({},e),{},uJ({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:ll(t,a.items,o)}))},{})),uZ(uZ({},e),{},uJ({},i,c))},{})},lf=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=u_(u,o,a);return t.domain([cH()(l),cW()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:uC(t.domain(),o,a)}:null},lp=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=uQ(i,e.dataKey,e.domain[a]);return E()(c)?null:e.scale(c)-o/2+n},lh=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},ld=function(t,e){var r,n=(null!=(r=t.type)&&r.defaultProps?uZ(uZ({},t.type.defaultProps),t.props):t.props).stackId;if(_(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},ly=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[cH()(e.concat([t[0]]).filter(T)),cW()(e.concat([t[1]]).filter(T))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},lv=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lm=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lb=function(t,e,r){if($()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(T(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(lv.test(t[0])){var o=+lv.exec(t[0])[1];n[0]=e[0]-o}else $()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(T(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(lm.test(t[1])){var i=+lm.exec(t[1])[1];n[1]=e[1]+i}else $()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},lg=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=tP()(e,function(t){return t.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},lx=function(t,e,r){return!t||!t.length||cK()(t,j()(r,"type.defaultProps.domain"))?e:t},lO=function(t,e){var r=t.type.defaultProps?uZ(uZ({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return uZ(uZ({},th(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:u2(t),value:uQ(e,n),type:c,payload:e,chartType:u,hide:l})};function lw(t){return(lw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lj(Object(r),!0).forEach(function(e){lA(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lA(t,e,r){var n;return(n=function(t,e){if("object"!=lw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lw(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var lP=["Webkit","Moz","O","ms"],lE=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=lP.reduce(function(t,n){return lS(lS({},t),{},lA({},n+r,e))},{});return n[t]=e,n};function lk(t){return(lk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lM(){return(lM=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lT(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l_(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lT(Object(r),!0).forEach(function(e){lB(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lT(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lC(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lR(n.key),n)}}function lD(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lD=function(){return!!t})()}function lI(t){return(lI=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lN(t,e){return(lN=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function lB(t,e,r){return(e=lR(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lR(t){var e=function(t,e){if("object"!=lk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lk(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lk(e)?e:e+""}var lL=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=nS().domain(tS()(0,c)).range([o,o+i-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}},lz=function(t){return t.changedTouches&&!!t.changedTouches.length},lU=function(t){var e,r;function n(t){var e,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[t],r=lI(r),lB(e=function(t,e){if(e&&("object"===lk(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,lD()?Reflect.construct(r,o||[],lI(this).constructor):r.apply(this,o)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),lB(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),lB(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),lB(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),lB(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),lB(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),lB(e,"handleSlideDragStart",function(t){var r=lz(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&lN(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Math.min(e,r),l=Math.max(e,r),s=n.getIndexInRange(o,u),f=n.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=uQ(r[t],o,t);return $()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=lz(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||!!(o<i)&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||!!(o>i)&&b===t};this.setState(lB(lB({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(lB({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.fill,a=t.stroke;return y().createElement("rect",{stroke:a,fill:i,x:e,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.data,a=t.children,c=t.padding,u=d.Children.only(a);return u?y().cloneElement(u,{x:e,y:r,width:n,height:o,margin:c,compact:!0,data:i}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,o,i=this,a=this.props,c=a.y,u=a.travellerWidth,l=a.height,s=a.traveller,f=a.ariaLabel,p=a.data,h=a.startIndex,d=a.endIndex,v=Math.max(t,this.props.x),m=l_(l_({},th(this.props,!1)),{},{x:v,y:c,width:u,height:l}),b=f||"Min value: ".concat(null==(r=p[h])?void 0:r.name,", Max value: ").concat(null==(o=p[d])?void 0:o.name);return y().createElement(tD,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(s,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,o=r.height,i=r.stroke,a=r.travellerWidth,c=Math.min(t,e)+a,u=Math.max(Math.abs(e-t)-a,0);return y().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:c,y:n,width:u,height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,o=t.height,i=t.travellerWidth,a=t.stroke,c=this.state,u=c.startX,l=c.endX,s={pointerEvents:"none",fill:a};return y().createElement(tD,{className:"recharts-brush-texts"},y().createElement(n4,lM({textAnchor:"end",verticalAnchor:"middle",x:Math.min(u,l)-5,y:n+o/2},s),this.getTextOfTick(e)),y().createElement(n4,lM({textAnchor:"start",verticalAnchor:"middle",x:Math.max(u,l)+i+5,y:n+o/2},s),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,o=t.x,i=t.y,a=t.width,c=t.height,u=t.alwaysShowText,l=this.state,s=l.startX,f=l.endX,p=l.isTextActive,d=l.isSlideMoving,v=l.isTravellerMoving,m=l.isTravellerFocused;if(!e||!e.length||!T(o)||!T(i)||!T(a)||!T(c)||a<=0||c<=0)return null;var b=(0,h.A)("recharts-brush",r),g=1===y().Children.count(n),x=lE("userSelect","none");return y().createElement(tD,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:x},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(s,f),this.renderTravellerLayer(s,"startX"),this.renderTravellerLayer(f,"endX"),(p||d||v||m||u)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,o=t.height,i=t.stroke,a=Math.floor(r+o/2)-1;return y().createElement(y().Fragment,null,y().createElement("rect",{x:e,y:r,width:n,height:o,fill:i,stroke:"none"}),y().createElement("line",{x1:e+1,y1:a,x2:e+n-1,y2:a,fill:"none",stroke:"#fff"}),y().createElement("line",{x1:e+1,y1:a+2,x2:e+n-1,y2:a+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return y().isValidElement(t)?y().cloneElement(t,e):$()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return l_({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?lL({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&lC(n.prototype,e),r&&lC(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(d.PureComponent);function lF(t){return(lF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l$(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lq(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l$(Object(r),!0).forEach(function(e){lW(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l$(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lW(t,e,r){var n;return(n=function(t,e){if("object"!=lF(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lF(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lX(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}lB(lU,"displayName","Brush"),lB(lU,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var lH=Math.PI/180,lV=function(t,e,r,n){return{x:t+Math.cos(-lH*n)*r,y:e+Math.sin(-lH*n)*r}},lG=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},lY=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},lK=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=lY({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=Math.acos((r-o)/a);return n>i&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},lZ=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},lJ=function(t,e){var r,n=lK({x:t.x,y:t.y},e),o=n.radius,i=n.angle,a=e.innerRadius,c=e.outerRadius;if(o<a||o>c)return!1;if(0===o)return!0;var u=lZ(e),l=u.startAngle,s=u.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?lq(lq({},e),{},{radius:o,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null},lQ=function(t){return(0,d.isValidElement)(t)||$()(t)||"boolean"==typeof t?"":t.className};function l0(t){return(l0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var l1=["offset"];function l2(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function l5(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l3(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l5(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=l0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l0(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l5(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function l4(){return(l4=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var l6=function(t){var e=t.value,r=t.formatter,n=E()(t.children)?e:t.children;return $()(r)?r(n):n},l8=function(t,e,r){var n,o,i=t.position,a=t.viewBox,c=t.offset,u=t.className,l=a.cx,s=a.cy,f=a.innerRadius,p=a.outerRadius,d=a.startAngle,v=a.endAngle,m=a.clockWise,b=(f+p)/2,g=k(v-d)*Math.min(Math.abs(v-d),360),x=g>=0?1:-1;"insideStart"===i?(n=d+x*c,o=m):"insideEnd"===i?(n=v-x*c,o=!m):"end"===i&&(n=v+x*c,o=m),o=g<=0?o:!o;var O=lV(l,s,b,n),w=lV(l,s,b,n+(o?1:-1)*359),j="M".concat(O.x,",").concat(O.y,"\n    A").concat(b,",").concat(b,",0,1,").concat(+!o,",\n    ").concat(w.x,",").concat(w.y),S=E()(t.id)?D("recharts-radial-line-"):t.id;return y().createElement("text",l4({},r,{dominantBaseline:"central",className:(0,h.A)("recharts-radial-bar-label",u)}),y().createElement("defs",null,y().createElement("path",{id:S,d:j})),y().createElement("textPath",{xlinkHref:"#".concat(S)},e))},l7=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=lV(o,i,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=lV(o,i,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},l9=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,c=e.width,u=e.height,l=u>=0?1:-1,s=l*n,f=l>0?"end":"start",p=l>0?"start":"end",h=c>=0?1:-1,d=h*n,y=h>0?"end":"start",v=h>0?"start":"end";if("top"===o)return l3(l3({},{x:i+c/2,y:a-l*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===o)return l3(l3({},{x:i+c/2,y:a+u+s,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===o){var m={x:i-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"};return l3(l3({},m),r?{width:Math.max(m.x-r.x,0),height:u}:{})}if("right"===o){var b={x:i+c+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"};return l3(l3({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:u}:{})}var g=r?{width:c,height:u}:{};return"insideLeft"===o?l3({x:i+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===o?l3({x:i+c-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===o?l3({x:i+c/2,y:a+s,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===o?l3({x:i+c/2,y:a+u-s,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===o?l3({x:i+d,y:a+s,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===o?l3({x:i+c-d,y:a+s,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===o?l3({x:i+d,y:a+u-s,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===o?l3({x:i+c-d,y:a+u-s,textAnchor:y,verticalAnchor:f},g):W()(o)&&(T(o.x)||M(o.x))&&(T(o.y)||M(o.y))?l3({x:i+I(o.x,c),y:a+I(o.y,u),textAnchor:"end",verticalAnchor:"end"},g):l3({x:i+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function st(t){var e,r=t.offset,n=l3({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,l1)),o=n.viewBox,i=n.position,a=n.value,c=n.children,u=n.content,l=n.className,s=n.textBreakAll;if(!o||E()(a)&&E()(c)&&!(0,d.isValidElement)(u)&&!$()(u))return null;if((0,d.isValidElement)(u))return(0,d.cloneElement)(u,n);if($()(u)){if(e=(0,d.createElement)(u,n),(0,d.isValidElement)(e))return e}else e=l6(n);var f="cx"in o&&T(o.cx),p=th(n,!0);if(f&&("insideStart"===i||"insideEnd"===i||"end"===i))return l8(n,e,p);var v=f?l7(n):l9(n);return y().createElement(n4,l4({className:(0,h.A)("recharts-label",void 0===l?"":l)},p,v,{breakAll:s}),e)}st.displayName="Label";var se=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,d=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(T(d)&&T(y)){if(T(s)&&T(f))return{x:s,y:f,width:d,height:y};if(T(p)&&T(h))return{x:p,y:h,width:d,height:y}}return T(s)&&T(f)?{x:s,y:f,width:0,height:0}:T(e)&&T(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};st.parseViewBox=se,st.renderCallByParent=function(t,e){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var i=t.children,a=se(t),c=tu(i,st).map(function(t,r){return(0,d.cloneElement)(t,{viewBox:e||a,key:"label-".concat(r)})});if(!o)return c;return[(r=t.label,n=e||a,!r?null:!0===r?y().createElement(st,{key:"label-implicit",viewBox:n}):_(r)?y().createElement(st,{key:"label-implicit",viewBox:n,value:r}):(0,d.isValidElement)(r)?r.type===st?(0,d.cloneElement)(r,{key:"label-implicit",viewBox:n}):y().createElement(st,{key:"label-implicit",content:r,viewBox:n}):$()(r)?y().createElement(st,{key:"label-implicit",content:r,viewBox:n}):W()(r)?y().createElement(st,l4({viewBox:n},r,{key:"label-implicit"})):null)].concat(function(t){if(Array.isArray(t))return l2(t)}(c)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(c)||function(t,e){if(t){if("string"==typeof t)return l2(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l2(t,e)}}(c)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var sr=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},sn=r(69691),so=r.n(sn),si=r(47212),sa=r.n(si),sc=function(t){return null};sc.displayName="Cell";var su=r(5359),sl=r.n(su);function ss(t){return(ss="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var sf=["valueAccessor"],sp=["data","dataKey","clockWise","id","textBreakAll"];function sh(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sd(){return(sd=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sy(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=ss(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ss(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ss(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sy(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sm(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var sb=function(t){return Array.isArray(t.value)?sl()(t.value):t.value};function sg(t){var e=t.valueAccessor,r=void 0===e?sb:e,n=sm(t,sf),o=n.data,i=n.dataKey,a=n.clockWise,c=n.id,u=n.textBreakAll,l=sm(n,sp);return o&&o.length?y().createElement(tD,{className:"recharts-label-list"},o.map(function(t,e){var n=E()(i)?r(t,e):uQ(t&&t.payload,i),o=E()(c)?{}:{id:"".concat(c,"-").concat(e)};return y().createElement(st,sd({},th(t,!0),l,o,{parentViewBox:t.parentViewBox,value:n,textBreakAll:u,viewBox:st.parseViewBox(E()(a)?t:sv(sv({},t),{},{clockWise:a})),key:"label-".concat(e),index:e}))})):null}sg.displayName="LabelList",sg.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var o=tu(t.children,sg).map(function(t,r){return(0,d.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label,!r?null:!0===r?y().createElement(sg,{key:"labelList-implicit",data:e}):y().isValidElement(r)||$()(r)?y().createElement(sg,{key:"labelList-implicit",data:e,content:r}):W()(r)?y().createElement(sg,sd({data:e},r,{key:"labelList-implicit"})):null)].concat(function(t){if(Array.isArray(t))return sh(t)}(o)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return sh(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sh(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var sx=r(38404),sO=r.n(sx),sw=r(98451),sj=r.n(sw);function sS(t){return(sS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sA(){return(sA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sP(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sE(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sk(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sE(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sS(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sS(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sS(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sE(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sM=function(t,e,r,n,o){var i,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+o)+"L ".concat(t+r-a/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},sT={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},s_=function(t){var e,r=sk(sk({},sT),t),n=(0,d.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,d.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return sP(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sP(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];(0,d.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&a(t)}catch(t){}},[]);var c=r.x,u=r.y,l=r.upperWidth,s=r.lowerWidth,f=r.height,p=r.className,v=r.animationEasing,m=r.animationDuration,b=r.animationBegin,g=r.isUpdateAnimationActive;if(c!==+c||u!==+u||l!==+l||s!==+s||f!==+f||0===l&&0===s||0===f)return null;var x=(0,h.A)("recharts-trapezoid",p);return g?y().createElement(na,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:f,x:c,y:u},to:{upperWidth:l,lowerWidth:s,height:f,x:c,y:u},duration:m,animationEasing:v,isActive:g},function(t){var e=t.upperWidth,o=t.lowerWidth,a=t.height,c=t.x,u=t.y;return y().createElement(na,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:b,duration:m,easing:v},y().createElement("path",sA({},th(r,!0),{className:x,d:sM(c,u,e,o,a),ref:n})))}):y().createElement("g",null,y().createElement("path",sA({},th(r,!0),{className:x,d:sM(c,u,l,s,f)})))};function sC(t){return(sC="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sD(){return(sD=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sI(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sN(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sI(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sC(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sC(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sC(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sI(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sB=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/lH,f=u?o:o+i*s;return{center:lV(e,r,l,f),circleTangency:lV(e,r,n,f),lineTangency:lV(e,r,l*Math.cos(s*lH),u?o-i*s:o),theta:s}},sR=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,a=t.endAngle,c=k(a-i)*Math.min(Math.abs(a-i),359.999),u=i+c,l=lV(e,r,o,i),s=lV(e,r,o,u),f="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(i>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var p=lV(e,r,n,i),h=lV(e,r,n,u);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(i<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(e,",").concat(r," Z");return f},sL=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=k(l-u),f=sB({cx:e,cy:r,radius:o,angle:u,sign:s,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,d=f.theta,y=sB({cx:e,cy:r,radius:o,angle:l,sign:-s,cornerRadius:i,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(u-l):Math.abs(u-l)-d-b;if(g<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):sR({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:u,endAngle:l});var x="M ".concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var O=sB({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),w=O.circleTangency,j=O.lineTangency,S=O.theta,A=sB({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),P=A.circleTangency,E=A.lineTangency,M=A.theta,T=c?Math.abs(u-l):Math.abs(u-l)-S-M;if(T<0&&0===i)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(T>180),",").concat(+(s>0),",").concat(w.x,",").concat(w.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},sz={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},sU=function(t){var e,r=sN(sN({},sz),t),n=r.cx,o=r.cy,i=r.innerRadius,a=r.outerRadius,c=r.cornerRadius,u=r.forceCornerRadius,l=r.cornerIsExternal,s=r.startAngle,f=r.endAngle,p=r.className;if(a<i||s===f)return null;var d=(0,h.A)("recharts-sector",p),v=a-i,m=I(c,v,0,!0);return e=m>0&&360>Math.abs(s-f)?sL({cx:n,cy:o,innerRadius:i,outerRadius:a,cornerRadius:Math.min(m,v/2),forceCornerRadius:u,cornerIsExternal:l,startAngle:s,endAngle:f}):sR({cx:n,cy:o,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f}),y().createElement("path",sD({},th(r,!0),{className:d,d:e,role:"img"}))},sF=["option","shapeType","propTransformer","activeClassName","isActive"];function s$(t){return(s$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sq(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sq(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=s$(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s$(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sq(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sX(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return y().createElement(ny,r);case"trapezoid":return y().createElement(s_,r);case"sector":return y().createElement(sU,r);case"symbols":if("symbols"===e)return y().createElement(eR,r);break;default:return null}}function sH(t){var e,r=t.option,n=t.shapeType,o=t.propTransformer,i=t.activeClassName,a=t.isActive,c=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,sF);if((0,d.isValidElement)(r))e=(0,d.cloneElement)(r,sW(sW({},c),(0,d.isValidElement)(r)?r.props:r));else if($()(r))e=r(c);else if(sO()(r)&&!sj()(r)){var u=(void 0===o?function(t,e){return sW(sW({},e),t)}:o)(r,c);e=y().createElement(sX,{shapeType:n,elementProps:u})}else e=y().createElement(sX,{shapeType:n,elementProps:c});return a?y().createElement(tD,{className:void 0===i?"recharts-active-shape":i},e):e}function sV(t,e){return null!=e&&"trapezoids"in t.props}function sG(t,e){return null!=e&&"sectors"in t.props}function sY(t,e){return null!=e&&"points"in t.props}function sK(t,e){var r,n,o=t.x===(null==e||null==(r=e.labelViewBox)?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null==(n=e.labelViewBox)?void 0:n.y)||t.y===e.y;return o&&i}function sZ(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function sJ(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}var sQ=["x","y"];function s0(t){return(s0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s1(){return(s1=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s2(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s5(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s2(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=s0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s0(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s2(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function s3(t,e){var r=t.x,n=t.y,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,sQ),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||o.height),10),u=parseInt("".concat(e.width||o.width),10);return s5(s5(s5(s5(s5({},e),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function s4(t){return y().createElement(sH,s1({shapeType:"rectangle",propTransformer:s3,activeClassName:"recharts-active-bar"},t))}var s6=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o=T(r)||E()(r);return o?t(r,n):(o||tE(!1),e)}},s8=["value","background"];function s7(t){return(s7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s9(){return(s9=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ft(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fe(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ft(Object(r),!0).forEach(function(e){fa(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ft(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fc(n.key),n)}}function fn(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fn=function(){return!!t})()}function fo(t){return(fo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fi(t,e){return(fi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fa(t,e,r){return(e=fc(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fc(t){var e=function(t,e){if("object"!=s7(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s7(e)?e:e+""}var fu=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=fo(e),fa(t=function(t,e){if(e&&("object"===s7(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fn()?Reflect.construct(e,r||[],fo(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),fa(t,"id",D("recharts-bar-")),fa(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),fa(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&fi(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,a=r.activeBar,c=th(this.props,!1);return t&&t.map(function(t,r){var u=r===i,l=fe(fe(fe({},c),t),{},{isActive:u,option:u?a:n,index:r,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return y().createElement(tD,s9({className:"recharts-bar-rectangle"},Q(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),y().createElement(s4,l))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,o=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state.prevData;return y().createElement(na,{begin:i,duration:a,isActive:o,easing:c,from:{t:0},to:{t:1},key:"bar-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var o=e.t,i=r.map(function(t,e){var r=l&&l[e];if(r){var i=R(r.x,t.x),a=R(r.y,t.y),c=R(r.width,t.width),u=R(r.height,t.height);return fe(fe({},t),{},{x:i(o),y:a(o),width:c(o),height:u(o)})}if("horizontal"===n){var s=R(0,t.height)(o);return fe(fe({},t),{},{y:t.y+t.height-s,height:s})}var f=R(0,t.width)(o);return fe(fe({},t),{},{width:f})});return y().createElement(tD,null,t.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!cK()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,o=e.activeIndex,i=th(this.props.background,!1);return r.map(function(e,r){e.value;var a=e.background,c=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,s8);if(!a)return null;var u=fe(fe(fe(fe(fe({},c),{},{fill:"#eee"},a),i),Q(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return y().createElement(s4,s9({key:"background-bar-".concat(r),option:t.props.background,isActive:r===o},u))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,a=r.layout,c=tu(r.children,u$);if(!c)return null;var u="vertical"===a?n[0].height/2:n[0].width/2,l=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:uQ(t,e)}};return y().createElement(tD,{clipPath:t?"url(#clipPath-".concat(e,")"):null},c.map(function(t){return y().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:a,offset:u,dataPointFormatter:l})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,o=t.xAxis,i=t.yAxis,a=t.left,c=t.top,u=t.width,l=t.height,s=t.isAnimationActive,f=t.background,p=t.id;if(e||!r||!r.length)return null;var d=this.state.isAnimationFinished,v=(0,h.A)("recharts-bar",n),m=o&&o.allowDataOverflow,b=i&&i.allowDataOverflow,g=m||b,x=E()(p)?this.id:p;return y().createElement(tD,{className:v},m||b?y().createElement("defs",null,y().createElement("clipPath",{id:"clipPath-".concat(x)},y().createElement("rect",{x:m?a:a-u/2,y:b?c:c-l/2,width:m?u:2*u,height:b?l:2*l}))):null,y().createElement(tD,{className:"recharts-bar-rectangles",clipPath:g?"url(#clipPath-".concat(x,")"):null},f?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(g,x),(!s||d)&&sg.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&fr(n.prototype,e),r&&fr(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(d.PureComponent);function fl(t){return(fl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fs(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fd(n.key),n)}}function ff(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fp(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ff(Object(r),!0).forEach(function(e){fh(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ff(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fh(t,e,r){return(e=fd(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fd(t){var e=function(t,e){if("object"!=fl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fl(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fl(e)?e:e+""}fa(fu,"displayName","Bar"),fa(fu,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!t1.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),fa(fu,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=la(n,r);if(!h)return null;var d=e.layout,y=r.type.defaultProps,v=void 0!==y?fe(fe({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===d?a:i,O=l?x.scale.domain():null,w=lh({numericAxis:x}),j=tu(b,sc),S=f.map(function(t,e){l?f=lc(l[s+e],O):Array.isArray(f=uQ(t,m))||(f=[w,f]);var n=s6(g,fu.defaultProps.minPointSize)(f[1],e);if("horizontal"===d){var f,p,y,v,b,x,S,A=[a.scale(f[0]),a.scale(f[1])],P=A[0],E=A[1];p=lp({axis:i,ticks:c,bandSize:o,offset:h.offset,entry:t,index:e}),y=null!=(S=null!=E?E:P)?S:void 0,v=h.size;var M=P-E;if(b=Number.isNaN(M)?0:M,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var T=k(b||n)*(Math.abs(n)-Math.abs(b));y-=T,b+=T}}else{var _=[i.scale(f[0]),i.scale(f[1])],C=_[0],D=_[1];if(p=C,y=lp({axis:a,ticks:u,bandSize:o,offset:h.offset,entry:t,index:e}),v=D-C,b=h.size,x={x:i.x,y:y,width:i.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var I=k(v||n)*(Math.abs(n)-Math.abs(v));v+=I}}return fe(fe(fe({},t),{},{x:p,y:y,width:v,height:b,value:l?f:f[1],payload:t,background:x},j&&j[e]&&j[e].props),{},{tooltipPayload:[lO(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return fe({data:S,layout:d},p)});var fy=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},fv=function(){var t,e;function r(t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&fs(r.prototype,t),e&&fs(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();fh(fv,"EPS",1e-4);var fm=function(t){var e=Object.keys(t).reduce(function(e,r){return fp(fp({},e),{},fh({},r,fv.create(t[r])))},{});return fp(fp({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return so()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return sa()(t,function(t,r){return e[r].isInRange(t)})}})},fb=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o))};function fg(){return(fg=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fx(t){return(fx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fO(Object(r),!0).forEach(function(e){fP(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fj(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fj=function(){return!!t})()}function fS(t){return(fS=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fA(t,e){return(fA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fP(t,e,r){return(e=fE(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fE(t){var e=function(t,e){if("object"!=fx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fx(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fx(e)?e:e+""}var fk=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=fm({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return sr(t,"discard")&&!i.isInRange(a)?null:a},fM=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=fS(t),function(t,e){if(e&&("object"===fx(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fj()?Reflect.construct(t,e||[],fS(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&fA(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,o=t.r,i=t.alwaysShow,a=t.clipPathId,c=_(e),u=_(n);if(U(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!u)return null;var l=fk(this.props);if(!l)return null;var s=l.x,f=l.y,p=this.props,d=p.shape,v=p.className,m=fw(fw({clipPath:sr(this.props,"hidden")?"url(#".concat(a,")"):void 0},th(this.props,!0)),{},{cx:s,cy:f});return y().createElement(tD,{className:(0,h.A)("recharts-reference-dot",v)},r.renderDot(d,m),st.renderCallByParent(this.props,{x:s-o,y:f-o,width:2*o,height:2*o}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fE(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(y().Component);fP(fM,"displayName","ReferenceDot"),fP(fM,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fP(fM,"renderDot",function(t,e){var r;return y().isValidElement(t)?y().cloneElement(t,e):$()(t)?t(e):y().createElement(e6,fg({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var fT=r(67367),f_=r.n(fT),fC=r(22964),fD=r.n(fC),fI=r(86451),fN=r.n(fI)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),fB=(0,d.createContext)(void 0),fR=(0,d.createContext)(void 0),fL=(0,d.createContext)(void 0),fz=(0,d.createContext)({}),fU=(0,d.createContext)(void 0),fF=(0,d.createContext)(0),f$=(0,d.createContext)(0),fq=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,o=e.offset,i=t.clipPathId,a=t.children,c=t.width,u=t.height,l=fN(o);return y().createElement(fB.Provider,{value:r},y().createElement(fR.Provider,{value:n},y().createElement(fz.Provider,{value:o},y().createElement(fL.Provider,{value:l},y().createElement(fU.Provider,{value:i},y().createElement(fF.Provider,{value:u},y().createElement(f$.Provider,{value:c},a)))))))},fW=function(t){var e=(0,d.useContext)(fB);null==e&&tE(!1);var r=e[t];return null==r&&tE(!1),r},fX=function(){var t=(0,d.useContext)(fR);return fD()(t,function(t){return sa()(t.domain,Number.isFinite)})||N(t)},fH=function(t){var e=(0,d.useContext)(fR);null==e&&tE(!1);var r=e[t];return null==r&&tE(!1),r},fV=function(){return(0,d.useContext)(f$)},fG=function(){return(0,d.useContext)(fF)};function fY(t){return(fY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fK(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fK=function(){return!!t})()}function fZ(t){return(fZ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fJ(t,e){return(fJ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f0(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fQ(Object(r),!0).forEach(function(e){f1(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function f1(t,e,r){return(e=f2(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f2(t){var e=function(t,e){if("object"!=fY(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fY(e)?e:e+""}function f5(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f3(){return(f3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var f4=function(t,e){var r;return y().isValidElement(t)?y().cloneElement(t,e):$()(t)?t(e):y().createElement("line",f3({},e,{className:"recharts-reference-line-line"}))},f6=function(t,e,r,n,o,i,a,c,u){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=u.y,d=t.y.apply(h,{position:i});if(sr(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(sr(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:i})});return sr(u,"discard")&&f_()(g,function(e){return!t.isInRange(e)})?null:g}return null};function f8(t){var e,r=t.x,n=t.y,o=t.segment,i=t.xAxisId,a=t.yAxisId,c=t.shape,u=t.className,l=t.alwaysShow,s=(0,d.useContext)(fU),f=fW(i),p=fH(a),v=(0,d.useContext)(fL);if(!s||!v)return null;U(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var m=f6(fm({x:f.scale,y:p.scale}),_(r),_(n),o&&2===o.length,v,t.position,f.orientation,p.orientation,t);if(!m)return null;var b=function(t){if(Array.isArray(t))return t}(m)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(m,2)||function(t,e){if(t){if("string"==typeof t)return f5(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f5(t,e)}}(m,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),g=b[0],x=g.x,O=g.y,w=b[1],j=w.x,S=w.y,A=f0(f0({clipPath:sr(t,"hidden")?"url(#".concat(s,")"):void 0},th(t,!0)),{},{x1:x,y1:O,x2:j,y2:S});return y().createElement(tD,{className:(0,h.A)("recharts-reference-line",u)},f4(c,A),st.renderCallByParent(t,fy({x:(e={x1:x,y1:O,x2:j,y2:S}).x1,y:e.y1},{x:e.x2,y:e.y2})))}var f7=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=fZ(t),function(t,e){if(e&&("object"===fY(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fK()?Reflect.construct(t,e||[],fZ(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&fJ(r,t),e=[{key:"render",value:function(){return y().createElement(f8,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,f2(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(y().Component);function f9(){return(f9=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pt(t){return(pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pe(Object(r),!0).forEach(function(e){pa(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pe(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}f1(f7,"displayName","ReferenceLine"),f1(f7,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function pn(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(pn=function(){return!!t})()}function po(t){return(po=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function pi(t,e){return(pi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pa(t,e,r){return(e=pc(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pc(t){var e=function(t,e){if("object"!=pt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pt(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pt(e)?e:e+""}var pu=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=fm({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!sr(o,"discard")||f.isInRange(p)&&f.isInRange(h)?fy(p,h):null},pl=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=po(t),function(t,e){if(e&&("object"===pt(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,pn()?Reflect.construct(t,e||[],po(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&pi(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,o=t.y1,i=t.y2,a=t.className,c=t.alwaysShow,u=t.clipPathId;U(void 0===c,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=_(e),s=_(n),f=_(o),p=_(i),d=this.props.shape;if(!l&&!s&&!f&&!p&&!d)return null;var v=pu(l,s,f,p,this.props);if(!v&&!d)return null;var m=sr(this.props,"hidden")?"url(#".concat(u,")"):void 0;return y().createElement(tD,{className:(0,h.A)("recharts-reference-area",a)},r.renderRect(d,pr(pr({clipPath:m},th(this.props,!0)),v)),st.renderCallByParent(this.props,v))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pc(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(y().Component);function ps(t){return function(t){if(Array.isArray(t))return pf(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return pf(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pf(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pf(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}pa(pl,"displayName","ReferenceArea"),pa(pl,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),pa(pl,"renderRect",function(t,e){var r;return y().isValidElement(t)?y().cloneElement(t,e):$()(t)?t(e):y().createElement(ny,f9({},e,{className:"recharts-reference-area-rect"}))});var pp=function(t,e,r,n,o){var i=tu(t,f7),a=tu(t,fM),c=[].concat(ps(i),ps(a)),u=tu(t,pl),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&sr(e.props,"extendDomain")&&T(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&sr(e.props,"extendDomain")&&T(e.props[p])&&T(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return T(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},ph=r(11117),pd=new(r.n(ph)()),py="recharts.syncMouseEvents";function pv(t){return(pv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pm(t,e,r){return(e=pb(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pb(t){var e=function(t,e){if("object"!=pv(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pv(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pv(e)?e:e+""}var pg=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");pm(this,"activeIndex",0),pm(this,"coordinateList",[]),pm(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!=(e=null!=n?n:this.coordinateList)?e:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null==(t=window)?void 0:t.scrollX)||0,u=(null==(e=window)?void 0:e.scrollY)||0,l=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pb(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function px(){}function pO(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function pw(t){this._context=t}function pj(t){this._context=t}function pS(t){this._context=t}pw.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pO(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pO(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pj.prototype={areaStart:px,areaEnd:px,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:pO(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pS.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pO(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class pA{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function pP(t){this._context=t}function pE(t){this._context=t}function pk(t){return new pE(t)}pP.prototype={areaStart:px,areaEnd:px,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function pM(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function pT(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function p_(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function pC(t){this._context=t}function pD(t){this._context=new pI(t)}function pI(t){this._context=t}function pN(t){this._context=t}function pB(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function pR(t,e){this._context=t,this._t=e}function pL(t){return t[0]}function pz(t){return t[1]}function pU(t,e){var r=ew(!0),n=null,o=pk,i=null,a=ek(c);function c(c){var u,l,s,f=(c=cT(c)).length,p=!1;for(null==n&&(i=o(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,u,c),+e(l,u,c));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?pL:ew(t),e="function"==typeof e?e:void 0===e?pz:ew(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:ew(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:ew(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:ew(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function pF(t,e,r){var n=null,o=ew(!0),i=null,a=pk,c=null,u=ek(l);function l(l){var s,f,p,h,d,y=(l=cT(l)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return pU().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?pL:ew(+t),e="function"==typeof e?e:void 0===e?ew(0):ew(+e),r="function"==typeof r?r:void 0===r?pz:ew(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:ew(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:ew(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:ew(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:ew(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:ew(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:ew(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:ew(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),l):i},l}function p$(t){return(p$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pq(){return(pq=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pW(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pX(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pW(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=p$(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p$(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pW(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}pE.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},pC.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:p_(this,this._t0,pT(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,p_(this,pT(this,r=pM(this,t,e)),r);break;default:p_(this,this._t0,r=pM(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(pD.prototype=Object.create(pC.prototype)).point=function(t,e){pC.prototype.point.call(this,e,t)},pI.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},pN.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=pB(t),o=pB(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},pR.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var pH={curveBasisClosed:function(t){return new pj(t)},curveBasisOpen:function(t){return new pS(t)},curveBasis:function(t){return new pw(t)},curveBumpX:function(t){return new pA(t,!0)},curveBumpY:function(t){return new pA(t,!1)},curveLinearClosed:function(t){return new pP(t)},curveLinear:pk,curveMonotoneX:function(t){return new pC(t)},curveMonotoneY:function(t){return new pD(t)},curveNatural:function(t){return new pN(t)},curveStep:function(t){return new pR(t,.5)},curveStepAfter:function(t){return new pR(t,1)},curveStepBefore:function(t){return new pR(t,0)}},pV=function(t){return t.x===+t.x&&t.y===+t.y},pG=function(t){return t.x},pY=function(t){return t.y},pK=function(t,e){if($()(t))return t;var r="curve".concat(ea()(t));return("curveMonotone"===r||"curveBump"===r)&&e?pH["".concat(r).concat("vertical"===e?"Y":"X")]:pH[r]||pk},pZ=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=pK(void 0===r?"linear":r,a),s=u?o.filter(function(t){return pV(t)}):o;if(Array.isArray(i)){var f=u?i.filter(function(t){return pV(t)}):i,p=s.map(function(t,e){return pX(pX({},t),{},{base:f[e]})});return(e="vertical"===a?pF().y(pY).x1(pG).x0(function(t){return t.base.x}):pF().x(pG).y1(pY).y0(function(t){return t.base.y})).defined(pV).curve(l),e(p)}return(e="vertical"===a&&T(i)?pF().y(pY).x1(pG).x0(i):T(i)?pF().x(pG).y1(pY).y0(i):pU().x(pG).y(pY)).defined(pV).curve(l),e(s)},pJ=function(t){var e=t.className,r=t.points,n=t.path,o=t.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?pZ(t):n;return d.createElement("path",pq({},th(t,!1),J(t),{className:(0,h.A)("recharts-curve",e),d:i,ref:o}))};function pQ(t){return(pQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var p0=["x","y","top","left","width","height","className"];function p1(){return(p1=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p2(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var p5=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,o=void 0===n?0:n,i=t.top,a=void 0===i?0:i,c=t.left,u=void 0===c?0:c,l=t.width,s=void 0===l?0:l,f=t.height,p=void 0===f?0:f,d=t.className,v=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p2(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=pQ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pQ(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p2(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:o,top:a,left:u,width:s,height:p},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,p0));return T(r)&&T(o)&&T(s)&&T(p)&&T(a)&&T(u)?y().createElement("path",p1({},th(v,!0),{className:(0,h.A)("recharts-cross",d),d:"M".concat(r,",").concat(a,"v").concat(p,"M").concat(u,",").concat(o,"h").concat(s)})):null};function p3(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[lV(e,r,n,o),lV(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}function p4(t){return(p4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p8(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p6(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=p4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p4(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function p7(t){var e,r,n,o,i=t.element,a=t.tooltipEventType,c=t.isActive,u=t.activeCoordinate,l=t.activePayload,s=t.offset,f=t.activeTooltipIndex,p=t.tooltipAxisBandSize,y=t.layout,v=t.chartName,m=null!=(r=i.props.cursor)?r:null==(n=i.type.defaultProps)?void 0:n.cursor;if(!i||!m||!c||!u||"ScatterChart"!==v&&"axis"!==a)return null;var b=pJ;if("ScatterChart"===v)o=u,b=p5;else if("BarChart"===v)e=p/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===y?u.x-e:s.left+.5,y:"horizontal"===y?s.top+.5:u.y-e,width:"horizontal"===y?p:s.width-1,height:"horizontal"===y?s.height-1:p},b=ny;else if("radial"===y){var g=p3(u),x=g.cx,O=g.cy,w=g.radius;o={cx:x,cy:O,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:w,outerRadius:w},b=sU}else o={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return p3(e);else{var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=lV(c,u,l,f),h=lV(c,u,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}(y,u,s)},b=pJ;var j=p8(p8(p8(p8({stroke:"#ccc",pointerEvents:"none"},s),o),th(m,!1)),{},{payload:l,payloadIndex:f,className:(0,h.A)("recharts-tooltip-cursor",m.className)});return(0,d.isValidElement)(m)?(0,d.cloneElement)(m,j):(0,d.createElement)(b,j)}var p9=["item"],ht=["children","className","width","height","style","compact","title","desc"];function he(t){return(he="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hr(){return(hr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||hl(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ho(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function hi(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hi=function(){return!!t})()}function ha(t){return(ha=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hc(t,e){return(hc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hu(t){return function(t){if(Array.isArray(t))return hs(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||hl(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hl(t,e){if(t){if("string"==typeof t)return hs(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hs(t,e)}}function hs(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hp(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hf(Object(r),!0).forEach(function(e){hh(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hf(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hh(t,e,r){return(e=hd(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hd(t){var e=function(t,e){if("object"!=he(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=he(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==he(e)?e:e+""}var hy={xAxis:["bottom","top"],yAxis:["left","right"]},hv={width:"100%",height:"100%"},hm={x:0,y:0};function hb(t){return t}var hg=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return hp(hp(hp({},n),lV(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return hp(hp(hp({},n),lV(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return hm},hx=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(hu(t),hu(r)):t},[]);return i.length>0?i:t&&t.length&&T(n)&&T(o)?t.slice(n,o+1):[]};function hO(t){return"number"===t?[0,"auto"]:void 0}var hw=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=hx(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var u,l,s=null!=(u=c.props.data)?u:e;return(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),l=i.dataKey&&!i.allowDuplicatedCategory?L(void 0===s?a:s,i.dataKey,n):s&&s[r]||a[r])?[].concat(hu(o),[lO(c,l)]):o},[])},hj=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=u1(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=hw(t,e,l,s),p=hg(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},hS=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=u9(l,o);return r.reduce(function(e,r){var h=void 0!==r.type.defaultProps?hp(hp({},r.type.defaultProps),r.props):r.props,d=h.type,y=h.dataKey,v=h.allowDataOverflow,m=h.allowDuplicatedCategory,b=h.scale,g=h.ticks,x=h.includeHidden,O=h[i];if(e[O])return e;var w=hx(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null==(e=t.type.defaultProps)?void 0:e[i])===O}),dataStartIndex:c,dataEndIndex:u}),j=w.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&T(n)&&T(o))return!0}return!1})(h.domain,v,d)&&(P=lb(h.domain,null,v),p&&("number"===d||"auto"!==b)&&(M=u0(w,y,"category")));var S=hO(d);if(!P||0===P.length){var A,P,k,M,_,C=null!=(_=h.domain)?_:S;if(y){if(P=u0(w,y,d),"category"===d&&p){var D=B(P);m&&D?(k=P,P=tS()(0,j)):m||(P=lx(C,P,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(hu(t),[e])},[]))}else if("category"===d)P=m?P.filter(function(t){return""!==t&&!E()(t)}):lx(C,P,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||E()(e)?t:[].concat(hu(t),[e])},[]);else if("number"===d){var I=u8(w,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null==(e=t.type.defaultProps)?void 0:e[i],o="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===O&&(x||!o)}),y,o,l);I&&(P=I)}p&&("number"===d||"auto"!==b)&&(M=u0(w,y,"category"))}else P=p?tS()(0,j):a&&a[O]&&a[O].hasStack&&"number"===d?"expand"===f?[0,1]:ly(a[O].stackGroups,c,u):u7(w,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===O&&(x||!r)}),d,l,!0);"number"===d?(P=pp(s,P,O,o,g),C&&(P=lb(C,P,v))):"category"===d&&C&&P.every(function(t){return C.indexOf(t)>=0})&&(P=C)}return hp(hp({},e),{},hh({},O,hp(hp({},h),{},{axisType:o,domain:P,categoricalDomain:M,duplicateDomain:k,originalDomain:null!=(A=h.domain)?A:S,isCategorical:p,layout:l})))},{})},hA=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=hx(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,h=u9(l,o),d=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?hp(hp({},e.type.defaultProps),e.props):e.props)[i],m=hO("number");return t[v]?t:(d++,y=h?tS()(0,p):a&&a[v]&&a[v].hasStack?pp(s,y=ly(a[v].stackGroups,c,u),v,o):pp(s,y=lb(m,u7(f,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null==(e=t.type.defaultProps)?void 0:e[i],o="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===v&&!o}),"number",l),n.defaultProps.allowDataOverflow),v,o),hp(hp({},t),{},hh({},v,hp(hp({axisType:o},n.defaultProps),{},{hide:!0,orientation:j()(hy,"".concat(o,".").concat(d%2),null),domain:y,originalDomain:m,isCategorical:h,layout:l}))))},{})},hP=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=tu(l,o),p={};return f&&f.length?p=hS(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(p=hA(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},hE=function(t){var e=N(t),r=le(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:tP()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:lg(e,r)}},hk=function(t){var e=t.children,r=t.defaultShowTooltip,n=tl(e,lU),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},hM=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},hT=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,f=r.margin||{},p=tl(s,lU),h=tl(s,e3),d=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:hp(hp({},t),{},hh({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:hp(hp({},t),{},hh({},n,j()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=hp(hp({},y),d),m=v.bottom;p&&(v.bottom+=p.props.height||lU.defaultProps.height),h&&e&&(v=u4(v,n,r,e));var b=u-v.left-v.right,g=l-v.top-v.bottom;return hp(hp({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},h_=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,o=void 0===n?"axis":n,i=t.validateTooltipEventTypes,a=void 0===i?["axis"]:i,c=t.axisComponents,u=t.legendContent,l=t.formatAxisMap,s=t.defaultProps,f=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,u=e.dataEndIndex,l=t.barSize,s=t.layout,f=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,d=hM(s),y=d.numericAxisName,v=d.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=to(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,d){var g=hx(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:u}),x=void 0!==r.type.defaultProps?hp(hp({},r.type.defaultProps),r.props):r.props,O=x.dataKey,w=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],A=c.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||tE(!1);var i=n[o];return hp(hp({},t),{},hh(hh({},r.axisType,i),"".concat(r.axisType,"Ticks"),le(i)))},{}),P=A[v],k=A["".concat(v,"Ticks")],M=n&&n[j]&&n[j].hasStack&&ld(r,n[j].stackGroups),T=to(r.type).indexOf("Bar")>=0,_=lg(P,k),C=[],D=m&&u5({barSize:l,stackGroups:n,totalSize:"xAxis"===v?A[v].width:"yAxis"===v?A[v].height:void 0});if(T){var I,N,B=E()(w)?h:w,R=null!=(I=null!=(N=lg(P,k,!0))?N:B)?I:0;C=u3({barGap:f,barCategoryGap:p,bandSize:R!==_?R:_,sizeList:D[S],maxBarSize:B}),R!==_&&(C=C.map(function(t){return hp(hp({},t),{},{position:hp(hp({},t.position),{},{offset:t.position.offset-R/2})})}))}var L=r&&r.type&&r.type.getComposedData;L&&b.push({props:hp(hp({},L(hp(hp({},A),{},{displayedData:g,props:t,dataKey:O,item:r,bandSize:_,barPosition:C,offset:o,stackedData:M,layout:s,dataStartIndex:a,dataEndIndex:u}))),{},hh(hh(hh({key:r.key||"item-".concat(d)},y,A[y]),v,A[v]),"animationId",i)),childIndex:tc(t.children).indexOf(r),item:r})}),b},p=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,u=t.updateId;if(!ts({props:o}))return null;var s=o.children,p=o.layout,h=o.stackOffset,d=o.data,y=o.reverseStackOrder,v=hM(p),m=v.numericAxisName,b=v.cateAxisName,g=tu(s,r),x=ls(d,g,"".concat(m,"Id"),"".concat(b,"Id"),h,y),O=c.reduce(function(t,e){var r="".concat(e.axisType,"Map");return hp(hp({},t),{},hh({},r,hP(o,hp(hp({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),w=hT(hp(hp({},O),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(O).forEach(function(t){O[t]=l(o,O[t],w,t.replace("Map",""),e)});var j=hE(O["".concat(b,"Map")]),S=f(o,hp(hp({},O),{},{dataStartIndex:i,dataEndIndex:a,updateId:u,graphicalItems:g,stackGroups:x,offset:w}));return hp(hp({formattedGraphicalItems:S,graphicalItems:g,offset:w,stackGroups:x},j),O)},v=function(t){var r;function n(t){var r,o,i,a,c;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return a=n,c=[t],a=ha(a),hh(i=function(t,e){if(e&&("object"===he(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hi()?Reflect.construct(a,c||[],ha(this).constructor):a.apply(this,c)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),hh(i,"accessibilityManager",new pg),hh(i,"handleLegendBBoxUpdate",function(t){if(t){var e=i.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;i.setState(hp({legendBBox:t},p({props:i.props,dataStartIndex:r,dataEndIndex:n,updateId:o},hp(hp({},i.state),{},{legendBBox:t}))))}}),hh(i,"handleReceiveSyncEvent",function(t,e,r){i.props.syncId===t&&(r!==i.eventEmitterSymbol||"function"==typeof i.props.syncMethod)&&i.applySyncEvent(e)}),hh(i,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==i.state.dataStartIndex||r!==i.state.dataEndIndex){var n=i.state.updateId;i.setState(function(){return hp({dataStartIndex:e,dataEndIndex:r},p({props:i.props,dataStartIndex:e,dataEndIndex:r,updateId:n},i.state))}),i.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),hh(i,"handleMouseEnter",function(t){var e=i.getMouseInfo(t);if(e){var r=hp(hp({},e),{},{isTooltipActive:!0});i.setState(r),i.triggerSyncEvent(r);var n=i.props.onMouseEnter;$()(n)&&n(r,t)}}),hh(i,"triggeredAfterMouseMove",function(t){var e=i.getMouseInfo(t),r=e?hp(hp({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};i.setState(r),i.triggerSyncEvent(r);var n=i.props.onMouseMove;$()(n)&&n(r,t)}),hh(i,"handleItemMouseEnter",function(t){i.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),hh(i,"handleItemMouseLeave",function(){i.setState(function(){return{isTooltipActive:!1}})}),hh(i,"handleMouseMove",function(t){t.persist(),i.throttleTriggeredAfterMouseMove(t)}),hh(i,"handleMouseLeave",function(t){i.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};i.setState(e),i.triggerSyncEvent(e);var r=i.props.onMouseLeave;$()(r)&&r(e,t)}),hh(i,"handleOuterEvent",function(t){var e,r,n=tm(t),o=j()(i.props,"".concat(n));n&&$()(o)&&o(null!=(e=/.*touch.*/i.test(n)?i.getMouseInfo(t.changedTouches[0]):i.getMouseInfo(t))?e:{},t)}),hh(i,"handleClick",function(t){var e=i.getMouseInfo(t);if(e){var r=hp(hp({},e),{},{isTooltipActive:!0});i.setState(r),i.triggerSyncEvent(r);var n=i.props.onClick;$()(n)&&n(r,t)}}),hh(i,"handleMouseDown",function(t){var e=i.props.onMouseDown;$()(e)&&e(i.getMouseInfo(t),t)}),hh(i,"handleMouseUp",function(t){var e=i.props.onMouseUp;$()(e)&&e(i.getMouseInfo(t),t)}),hh(i,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&i.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),hh(i,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&i.handleMouseDown(t.changedTouches[0])}),hh(i,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&i.handleMouseUp(t.changedTouches[0])}),hh(i,"handleDoubleClick",function(t){var e=i.props.onDoubleClick;$()(e)&&e(i.getMouseInfo(t),t)}),hh(i,"handleContextMenu",function(t){var e=i.props.onContextMenu;$()(e)&&e(i.getMouseInfo(t),t)}),hh(i,"triggerSyncEvent",function(t){void 0!==i.props.syncId&&pd.emit(py,i.props.syncId,t,i.eventEmitterSymbol)}),hh(i,"applySyncEvent",function(t){var e=i.props,r=e.layout,n=e.syncMethod,o=i.state.updateId,a=t.dataStartIndex,c=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)i.setState(hp({dataStartIndex:a,dataEndIndex:c},p({props:i.props,dataStartIndex:a,dataEndIndex:c,updateId:o},i.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=i.state,h=f.offset,d=f.tooltipTicks;if(!h)return;if("function"==typeof n)s=n(d,t);else if("value"===n){s=-1;for(var y=0;y<d.length;y++)if(d[y].value===t.activeLabel){s=y;break}}var v=hp(hp({},h),{},{x:h.left,y:h.top}),m=Math.min(u,v.x+v.width),b=Math.min(l,v.y+v.height),g=d[s]&&d[s].value,x=hw(i.state,i.props.data,s),O=d[s]?{x:"horizontal"===r?d[s].coordinate:m,y:"horizontal"===r?b:d[s].coordinate}:hm;i.setState(hp(hp({},t),{},{activeLabel:g,activeCoordinate:O,activePayload:x,activeTooltipIndex:s}))}else i.setState(t)}),hh(i,"renderCursor",function(t){var r,n=i.state,o=n.isTooltipActive,a=n.activeCoordinate,c=n.activePayload,u=n.offset,l=n.activeTooltipIndex,s=n.tooltipAxisBandSize,f=i.getTooltipEventType(),p=null!=(r=t.props.active)?r:o,h=i.props.layout,d=t.key||"_recharts-cursor";return y().createElement(p7,{key:d,activeCoordinate:a,activePayload:c,activeTooltipIndex:l,chartName:e,element:t,isActive:p,layout:h,offset:u,tooltipAxisBandSize:s,tooltipEventType:f})}),hh(i,"renderPolarAxis",function(t,e,r){var n=j()(t,"type.axisType"),o=j()(i.state,"".concat(n,"Map")),a=t.type.defaultProps,c=void 0!==a?hp(hp({},a),t.props):t.props,u=o&&o[c["".concat(n,"Id")]];return(0,d.cloneElement)(t,hp(hp({},u),{},{className:(0,h.A)(n,u.className),key:t.key||"".concat(e,"-").concat(r),ticks:le(u,!0)}))}),hh(i,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,o=e.polarRadius,a=i.state,c=a.radiusAxisMap,u=a.angleAxisMap,l=N(c),s=N(u),f=s.cx,p=s.cy,h=s.innerRadius,y=s.outerRadius;return(0,d.cloneElement)(t,{polarAngles:Array.isArray(n)?n:le(s,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(o)?o:le(l,!0).map(function(t){return t.coordinate}),cx:f,cy:p,innerRadius:h,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),hh(i,"renderLegend",function(){var t=i.state.formattedGraphicalItems,e=i.props,r=e.children,n=e.width,o=e.height,a=i.props.margin||{},c=uH({children:r,formattedGraphicalItems:t,legendWidth:n-(a.left||0)-(a.right||0),legendContent:u});if(!c)return null;var l=c.item,s=ho(c,p9);return(0,d.cloneElement)(l,hp(hp({},s),{},{chartWidth:n,chartHeight:o,margin:a,onBBoxUpdate:i.handleLegendBBoxUpdate}))}),hh(i,"renderTooltip",function(){var t,e=i.props,r=e.children,n=e.accessibilityLayer,o=tl(r,eo);if(!o)return null;var a=i.state,c=a.isTooltipActive,u=a.activeCoordinate,l=a.activePayload,s=a.activeLabel,f=a.offset,p=null!=(t=o.props.active)?t:c;return(0,d.cloneElement)(o,{viewBox:hp(hp({},f),{},{x:f.left,y:f.top}),active:p,label:s,payload:p?l:[],coordinate:u,accessibilityLayer:n})}),hh(i,"renderBrush",function(t){var e=i.props,r=e.margin,n=e.data,o=i.state,a=o.offset,c=o.dataStartIndex,u=o.dataEndIndex,l=o.updateId;return(0,d.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:ln(i.handleBrushChange,t.props.onChange),data:n,x:T(t.props.x)?t.props.x:a.left,y:T(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:T(t.props.width)?t.props.width:a.width,startIndex:c,endIndex:u,updateId:"brush-".concat(l)})}),hh(i,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=i.clipPathId,o=i.state,a=o.xAxisMap,c=o.yAxisMap,u=o.offset,l=t.type.defaultProps||{},s=t.props,f=s.xAxisId,p=void 0===f?l.xAxisId:f,h=s.yAxisId,y=void 0===h?l.yAxisId:h;return(0,d.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[p],yAxis:c[y],viewBox:{x:u.left,y:u.top,width:u.width,height:u.height},clipPathId:n})}),hh(i,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?hp(hp({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=hp(hp({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:u2(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},th(s,!1)),J(s));return c.push(n.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),o?c.push(n.renderActiveDot(s,hp(hp({},f),{},{cx:o.x,cy:o.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),hh(i,"renderGraphicChild",function(t,e,r){var n=i.filterFormatItem(t,e,r);if(!n)return null;var o=i.getTooltipEventType(),a=i.state,c=a.isTooltipActive,u=a.tooltipAxis,l=a.activeTooltipIndex,s=a.activeLabel,f=tl(i.props.children,eo),p=n.props,h=p.points,y=p.isRange,v=p.baseLine,m=void 0!==n.item.type.defaultProps?hp(hp({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,O=m.activeShape,w=!!(!g&&c&&f&&(b||x||O)),j={};"axis"!==o&&f&&"click"===f.props.trigger?j={onClick:ln(i.handleItemMouseEnter,t.props.onClick)}:"axis"!==o&&(j={onMouseLeave:ln(i.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:ln(i.handleItemMouseEnter,t.props.onMouseEnter)});var S=(0,d.cloneElement)(t,hp(hp({},n.props),j));if(w)if(l>=0){if(u.dataKey&&!u.allowDuplicatedCategory){var A="function"==typeof u.dataKey?function(t){return"function"==typeof u.dataKey?u.dataKey(t.payload):null}:"payload.".concat(u.dataKey.toString());k=L(h,A,s),M=y&&v&&L(v,A,s)}else k=null==h?void 0:h[l],M=y&&v&&v[l];if(O||x){var P=void 0!==t.props.activeIndex?t.props.activeIndex:l;return[(0,d.cloneElement)(t,hp(hp(hp({},n.props),j),{},{activeIndex:P})),null,null]}if(!E()(k))return[S].concat(hu(i.renderActivePoints({item:n,activePoint:k,basePoint:M,childIndex:l,isRange:y})))}else{var k,M,T,_=(null!=(T=i.getItemByXY(i.state.activeCoordinate))?T:{graphicalItem:S}).graphicalItem,C=_.item,D=void 0===C?t:C,I=_.childIndex,N=hp(hp(hp({},n.props),j),{},{activeIndex:I});return[(0,d.cloneElement)(D,N),null,null]}return y?[S,null,null]:[S,null]}),hh(i,"renderCustomized",function(t,e,r){return(0,d.cloneElement)(t,hp(hp({key:"recharts-customized-".concat(r)},i.props),i.state))}),hh(i,"renderMap",{CartesianGrid:{handler:hb,once:!0},ReferenceArea:{handler:i.renderReferenceElement},ReferenceLine:{handler:hb},ReferenceDot:{handler:i.renderReferenceElement},XAxis:{handler:hb},YAxis:{handler:hb},Brush:{handler:i.renderBrush,once:!0},Bar:{handler:i.renderGraphicChild},Line:{handler:i.renderGraphicChild},Area:{handler:i.renderGraphicChild},Radar:{handler:i.renderGraphicChild},RadialBar:{handler:i.renderGraphicChild},Scatter:{handler:i.renderGraphicChild},Pie:{handler:i.renderGraphicChild},Funnel:{handler:i.renderGraphicChild},Tooltip:{handler:i.renderCursor,once:!0},PolarGrid:{handler:i.renderPolarGrid,once:!0},PolarAngleAxis:{handler:i.renderPolarAxis},PolarRadiusAxis:{handler:i.renderPolarAxis},Customized:{handler:i.renderCustomized}}),i.clipPathId="".concat(null!=(r=t.id)?r:D("recharts"),"-clip"),i.throttleTriggeredAfterMouseMove=m()(i.triggeredAfterMouseMove,null!=(o=t.throttleDelay)?o:1e3/60),i.state={},i}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&hc(n,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(t=this.props.margin.left)?t:0,top:null!=(e=this.props.margin.top)?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=tl(e,eo);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=hw(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=hp(hp({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){td([tl(t.children,eo)],[tl(this.props.children,eo)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=tl(this.props.children,eo);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return a.indexOf(e)>=0?e:o}return o}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap,s=this.getTooltipEventType(),f=hj(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&u&&l){var p=N(u).scale,h=N(l).scale,d=p&&p.invert?p.invert(o.chartX):null,y=h&&h.invert?h.invert(o.chartY):null;return hp(hp({},o),{},{xValue:d,yValue:y},f)}return f?hp(hp({},o),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;return u&&l?lJ({x:o,y:i},N(u)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=tl(t,eo),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),hp(hp({},J(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){pd.on(py,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){pd.removeListener(py,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===to(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,o=e.height,i=e.width;return y().createElement("defs",null,y().createElement("clipPath",{id:t},y().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hn(e,2),n=r[0],o=r[1];return hp(hp({},t),{},hh({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hn(e,2),n=r[0],o=r[1];return hp(hp({},t),{},hh({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null==(e=this.state.xAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null==(e=this.state.yAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?hp(hp({},u.type.defaultProps),u.props):u.props,s=to(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return nh(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return lJ(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(sV(a,n)||sG(a,n)||sY(a,n)){var h=function(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,c=(sV(i,o)?e="trapezoids":sG(i,o)?e="sectors":sY(i,o)&&(e="points"),e),u=sV(i,o)?null==(r=o.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:sG(i,o)?null==(n=o.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:sY(i,o)?o.payload:{},l=a.filter(function(t,e){var r=cK()(u,t),n=i.props[c].filter(function(t){var e;return(sV(i,o)?e=sK:sG(i,o)?e=sZ:sY(i,o)&&(e=sJ),e)(t,o)}),a=i.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:hp(hp({},a),{},{childIndex:d}),payload:sY(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!ts(this))return null;var n=this.props,o=n.children,i=n.className,a=n.width,c=n.height,u=n.style,l=n.compact,s=n.title,f=n.desc,p=th(ho(n,ht),!1);if(l)return y().createElement(fq,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},y().createElement(tT,hr({},p,{width:a,height:c,title:s,desc:f}),this.renderClipPath(),tv(o,this.renderMap)));this.props.accessibilityLayer&&(p.tabIndex=null!=(t=this.props.tabIndex)?t:0,p.role=null!=(e=this.props.role)?e:"application",p.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},p.onFocus=function(){r.accessibilityManager.focus()});var d=this.parseEventsOfWrapper();return y().createElement(fq,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},y().createElement("div",hr({className:(0,h.A)("recharts-wrapper",i),style:hp({position:"relative",cursor:"default",width:a,height:c},u)},d,{ref:function(t){r.container=t}}),y().createElement(tT,hr({},p,{width:a,height:c,title:s,desc:f,style:hv}),this.renderClipPath(),tv(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hd(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(d.Component);hh(v,"displayName",e),hh(v,"defaultProps",hp({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},s)),hh(v,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var h=hk(t);return hp(hp(hp({},h),{},{updateId:0},p(hp(hp({props:t},h),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!H(l,e.prevMargin)){var d=hk(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=hp(hp({},hj(e,n,c)),{},{updateId:e.updateId+1}),m=hp(hp(hp({},d),y),v);return hp(hp(hp({},m),p(hp({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(!td(o,e.prevChildren)){var b,g,x,O,w=tl(o,lU),j=w&&null!=(b=null==(g=w.props)?void 0:g.startIndex)?b:s,S=w&&null!=(x=null==(O=w.props)?void 0:O.endIndex)?x:f,A=E()(n)||j!==s||S!==f?e.updateId+1:e.updateId;return hp(hp({updateId:A},p(hp(hp({props:t},e),{},{updateId:A,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),hh(v,"renderActiveDot",function(t,e,r){var n;return n=(0,d.isValidElement)(t)?(0,d.cloneElement)(t,e):$()(t)?t(e):y().createElement(e6,e),y().createElement(tD,{className:"recharts-active-dot",key:r},n)});var b=(0,d.forwardRef)(function(t,e){return y().createElement(v,hr({},t,{ref:e}))});return b.displayName=v.displayName,b};function hC(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e)if(void 0!==r&&!0!==r(t[o]))return;else n.push(t[o]);return n}function hD(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function hI(t){return(hI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hN(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hB(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hN(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=hI(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hI(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hN(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hR(t,e,r){var n,o,i,a,c,u=t.tick,l=t.ticks,s=t.viewBox,f=t.minTickGap,p=t.orientation,h=t.interval,d=t.tickFormatter,y=t.unit,v=t.angle;if(!l||!l.length||!u)return[];if(T(h)||t1.isSsr)return hC(l,("number"==typeof h&&T(h)?h:0)+1);var m=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?nC(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},x=function(t,n){var o,i=$()(d)?d(t.value,n):t.value;return"width"===b?(o=nC(i,{fontSize:e,letterSpacing:r}),fb({width:o.width+g.width,height:o.height+g.height},v)):nC(i,{fontSize:e,letterSpacing:r})[b]},O=l.length>=2?k(l[1].coordinate-l[0].coordinate):1,w=(n="width"===b,o=s.x,i=s.y,a=s.width,c=s.height,1===O?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i});return"equidistantPreserveStart"===h?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c;s<=a.length;)if(i=function(){var e,i=null==n?void 0:n[l];if(void 0===i)return{v:hC(n,s)};var a=l,p=function(){return void 0===e&&(e=r(i,a)),e},h=i.coordinate,d=0===l||hD(t,h,p,f,u);d||(l=0,f=c,s+=1),d&&(f=h+t*(p()/2+o),l+=s)}())return i.v;return[]}(O,w,x,l,f):("preserveStart"===h||"preserveStartEnd"===h?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=hB(hB({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),hD(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+o),a[c-1]=hB(hB({},s),{},{isShow:!0}))}for(var h=i?c-1:c,d=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var s=t*(i.coordinate-t*c()/2-u);a[e]=i=hB(hB({},i),{},{tickCoord:s<0?i.coordinate-s*t:i.coordinate})}else a[e]=i=hB(hB({},i),{},{tickCoord:i.coordinate});hD(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=hB(hB({},i),{},{isShow:!0}))},y=0;y<h;y++)d(y);return a}(O,w,x,l,f,"preserveStartEnd"===h):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);i[e]=l=hB(hB({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else i[e]=l=hB(hB({},l),{},{tickCoord:l.coordinate});hD(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+o),i[e]=hB(hB({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return i}(O,w,x,l,f)).filter(function(t){return t.isShow})}var hL=["viewBox"],hz=["viewBox"],hU=["ticks"];function hF(t){return(hF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h$(){return(h$=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hq(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hq(Object(r),!0).forEach(function(e){hK(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hq(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hX(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function hH(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hZ(n.key),n)}}function hV(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hV=function(){return!!t})()}function hG(t){return(hG=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hY(t,e){return(hY=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hK(t,e,r){return(e=hZ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hZ(t){var e=function(t,e){if("object"!=hF(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hF(e)?e:e+""}var hJ=function(t){var e,r;function n(t){var e,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[t],r=hG(r),(e=function(t,e){if(e&&("object"===hF(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hV()?Reflect.construct(r,o||[],hG(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&hY(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=hX(t,hL),o=this.props,i=o.viewBox,a=hX(o,hz);return!H(r,i)||!H(n,a)||!H(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,d=c.mirror,y=c.tickMargin,v=d?-1:1,m=t.tickSize||h,b=T(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+!d*f)-v*m)-v*y,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=u+!d*s)-v*m)-v*y,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=u+d*s)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(o=l+d*f)+v*m)+v*y,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.orientation,a=t.mirror,c=t.axisLine,u=hW(hW(hW({},th(this.props,!1)),th(c,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var l=+("top"===i&&!a||"bottom"===i&&a);u=hW(hW({},u),{},{x1:e,y1:r+l*o,x2:e+n,y2:r+l*o})}else{var s=+("left"===i&&!a||"right"===i&&a);u=hW(hW({},u),{},{x1:e+s*n,y1:r,x2:e+s*n,y2:r+o})}return y().createElement("line",h$({},u,{className:(0,h.A)("recharts-cartesian-axis-line",j()(c,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var o=this,i=this.props,a=i.tickLine,c=i.stroke,u=i.tick,l=i.tickFormatter,s=i.unit,f=hR(hW(hW({},this.props),{},{ticks:t}),e,r),p=this.getTickTextAnchor(),d=this.getTickVerticalAnchor(),v=th(this.props,!1),m=th(u,!1),b=hW(hW({},v),{},{fill:"none"},th(a,!1)),g=f.map(function(t,e){var r=o.getTickLineCoord(t),i=r.line,g=r.tick,x=hW(hW(hW(hW({textAnchor:p,verticalAnchor:d},v),{},{stroke:"none",fill:c},m),g),{},{index:e,payload:t,visibleTicksCount:f.length,tickFormatter:l});return y().createElement(tD,h$({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},Q(o.props,t,e)),a&&y().createElement("line",h$({},b,i,{className:(0,h.A)("recharts-cartesian-axis-tick-line",j()(a,"className"))})),u&&n.renderTickItem(u,x,"".concat($()(l)?l(t.value,e):t.value).concat(s||"")))});return y().createElement("g",{className:"recharts-cartesian-axis-ticks"},g)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,o=e.height,i=e.ticksGenerator,a=e.className;if(e.hide)return null;var c=this.props,u=c.ticks,l=hX(c,hU),s=u;return($()(i)&&(s=i(u&&u.length>0?this.props:l)),n<=0||o<=0||!s||!s.length)?null:y().createElement(tD,{className:(0,h.A)("recharts-cartesian-axis",a),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(s,this.state.fontSize,this.state.letterSpacing),st.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var n,o=(0,h.A)(e.className,"recharts-cartesian-axis-tick-value");return y().isValidElement(t)?y().cloneElement(t,hW(hW({},e),{},{className:o})):$()(t)?t(hW(hW({},e),{},{className:o})):y().createElement(n4,h$({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&hH(n.prototype,e),r&&hH(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(d.Component);function hQ(t){return(hQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}hK(hJ,"displayName","CartesianAxis"),hK(hJ,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function h0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(h0=function(){return!!t})()}function h1(t){return(h1=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h2(t,e){return(h2=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function h5(t,e,r){return(e=h3(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h3(t){var e=function(t,e){if("object"!=hQ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hQ(e)?e:e+""}function h4(){return(h4=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function h6(t){var e=t.xAxisId,r=fV(),n=fG(),o=fW(e);return null==o?null:d.createElement(hJ,h4({},o,{className:(0,h.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return le(t,!0)}}))}var h8=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=h1(t),function(t,e){if(e&&("object"===hQ(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,h0()?Reflect.construct(t,e||[],h1(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&h2(r,t),e=[{key:"render",value:function(){return d.createElement(h6,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h3(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(d.Component);function h7(t){return(h7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}h5(h8,"displayName","XAxis"),h5(h8,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function h9(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(h9=function(){return!!t})()}function dt(t){return(dt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function de(t,e){return(de=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dr(t,e,r){return(e=dn(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dn(t){var e=function(t,e){if("object"!=h7(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h7(e)?e:e+""}function di(){return(di=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var da=function(t){var e=t.yAxisId,r=fV(),n=fG(),o=fH(e);return null==o?null:d.createElement(hJ,di({},o,{className:(0,h.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return le(t,!0)}}))},dc=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=dt(t),function(t,e){if(e&&("object"===h7(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,h9()?Reflect.construct(t,e||[],dt(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&de(r,t),e=[{key:"render",value:function(){return d.createElement(da,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dn(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(d.Component);dr(dc,"displayName","YAxis"),dr(dc,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var du=h_({chartName:"BarChart",GraphicalChild:fu,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:h8},{axisType:"yAxis",AxisComp:dc}],formatAxisMap:function(t,e,r,n,o){var i=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!tl(u,fu);return l.reduce(function(i,a){var u,l,p,h,d,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,O=y.reversed,w="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,A=y.categoricalDomain.sort(z);if(A.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(A[e-1]||0),S))}),Number.isFinite(S)){var P=S/j,E="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=P*E/2),"no-gap"===y.padding){var k=I(t.barCategoryGap,P*E),M=P*E/2;u=M-k-(M-k)/E*k}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:y.range,O&&(l=[l[1],l[0]]);var T=lo(y,o,f),_=T.scale,C=T.realScaleType;_.domain(m).range(l),li(_);var D=lf(_,fp(fp({},y),{},{realScaleType:C}));"xAxis"===n?(d="top"===v&&!x||"bottom"===v&&x,p=r.left,h=s[w]-d*y.height):"yAxis"===n&&(d="left"===v&&!x||"right"===v&&x,p=s[w]-d*y.width,h=r.top);var N=fp(fp(fp({},y),D),{},{realScaleType:C,x:p,y:h,scale:_,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return N.bandSize=lg(N,D),y.hide||"xAxis"!==n?y.hide||(s[w]+=(d?-1:1)*N.width):s[w]+=(d?-1:1)*N.height,fp(fp({},i),{},fh({},a,N))},{})}}),dl=["x1","y1","x2","y2","key"],ds=["offset"];function df(t){return(df="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dh(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dp(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=df(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=df(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==df(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dp(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dd(){return(dd=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dy(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var dv=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,n=t.x,o=t.y,i=t.width,a=t.height,c=t.ry;return y().createElement("rect",{x:n,y:o,ry:c,width:i,height:a,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function dm(t,e){var r;if(y().isValidElement(t))r=y().cloneElement(t,e);else if($()(t))r=t(e);else{var n=e.x1,o=e.y1,i=e.x2,a=e.y2,c=e.key,u=th(dy(e,dl),!1),l=(u.offset,dy(u,ds));r=y().createElement("line",dd({},l,{x1:n,y1:o,x2:i,y2:a,fill:"none",key:c}))}return r}function db(t){var e=t.x,r=t.width,n=t.horizontal,o=void 0===n||n,i=t.horizontalPoints;if(!o||!i||!i.length)return null;var a=i.map(function(n,i){return dm(o,dh(dh({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(i),index:i}))});return y().createElement("g",{className:"recharts-cartesian-grid-horizontal"},a)}function dg(t){var e=t.y,r=t.height,n=t.vertical,o=void 0===n||n,i=t.verticalPoints;if(!o||!i||!i.length)return null;var a=i.map(function(n,i){return dm(o,dh(dh({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(i),index:i}))});return y().createElement("g",{className:"recharts-cartesian-grid-vertical"},a)}function dx(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,o=t.y,i=t.width,a=t.height,c=t.horizontalPoints,u=t.horizontal;if(!(void 0===u||u)||!e||!e.length)return null;var l=c.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==l[0]&&l.unshift(0);var s=l.map(function(t,c){var u=l[c+1]?l[c+1]-t:o+a-t;if(u<=0)return null;var s=c%e.length;return y().createElement("rect",{key:"react-".concat(c),y:t,x:n,height:u,width:i,stroke:"none",fill:e[s],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return y().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},s)}function dO(t){var e=t.vertical,r=t.verticalFill,n=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var l=u.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==l[0]&&l.unshift(0);var s=l.map(function(t,e){var u=l[e+1]?l[e+1]-t:o+a-t;if(u<=0)return null;var s=e%r.length;return y().createElement("rect",{key:"react-".concat(e),x:t,y:i,width:u,height:c,stroke:"none",fill:r[s],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return y().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},s)}var dw=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return lt(hR(dh(dh(dh({},hJ.defaultProps),r),{},{ticks:le(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},dj=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return lt(hR(dh(dh(dh({},hJ.defaultProps),r),{},{ticks:le(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},dS={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function dA(t){var e,r,n,o,i,a,c=fV(),u=fG(),l=(0,d.useContext)(fz),s=dh(dh({},t),{},{stroke:null!=(e=t.stroke)?e:dS.stroke,fill:null!=(r=t.fill)?r:dS.fill,horizontal:null!=(n=t.horizontal)?n:dS.horizontal,horizontalFill:null!=(o=t.horizontalFill)?o:dS.horizontalFill,vertical:null!=(i=t.vertical)?i:dS.vertical,verticalFill:null!=(a=t.verticalFill)?a:dS.verticalFill,x:T(t.x)?t.x:l.left,y:T(t.y)?t.y:l.top,width:T(t.width)?t.width:l.width,height:T(t.height)?t.height:l.height}),f=s.x,p=s.y,h=s.width,v=s.height,m=s.syncWithTicks,b=s.horizontalValues,g=s.verticalValues,x=N((0,d.useContext)(fB)),O=fX();if(!T(h)||h<=0||!T(v)||v<=0||!T(f)||f!==+f||!T(p)||p!==+p)return null;var w=s.verticalCoordinatesGenerator||dw,j=s.horizontalCoordinatesGenerator||dj,S=s.horizontalPoints,A=s.verticalPoints;if((!S||!S.length)&&$()(j)){var P=b&&b.length,E=j({yAxis:O?dh(dh({},O),{},{ticks:P?b:O.ticks}):void 0,width:c,height:u,offset:l},!!P||m);U(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(df(E),"]")),Array.isArray(E)&&(S=E)}if((!A||!A.length)&&$()(w)){var k=g&&g.length,M=w({xAxis:x?dh(dh({},x),{},{ticks:k?g:x.ticks}):void 0,width:c,height:u,offset:l},!!k||m);U(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(df(M),"]")),Array.isArray(M)&&(A=M)}return y().createElement("g",{className:"recharts-cartesian-grid"},y().createElement(dv,{fill:s.fill,fillOpacity:s.fillOpacity,x:s.x,y:s.y,width:s.width,height:s.height,ry:s.ry}),y().createElement(db,dd({},s,{offset:l,horizontalPoints:S,xAxis:x,yAxis:O})),y().createElement(dg,dd({},s,{offset:l,verticalPoints:A,xAxis:x,yAxis:O})),y().createElement(dx,dd({},s,{horizontalPoints:S})),y().createElement(dO,dd({},s,{verticalPoints:A})))}dA.displayName="CartesianGrid";var dP=["points","className","baseLinePoints","connectNulls"];function dE(){return(dE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dk(t){return function(t){if(Array.isArray(t))return dM(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return dM(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dM(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dM(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var dT=function(t){return t&&t.x===+t.x&&t.y===+t.y},d_=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach(function(t){dT(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])}),dT(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e},dC=function(t,e){var r=d_(t);e&&(r=[r.reduce(function(t,e){return[].concat(dk(t),dk(e))},[])]);var n=r.map(function(t){return t.reduce(function(t,e,r){return"".concat(t).concat(0===r?"M":"L").concat(e.x,",").concat(e.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},dD=function(t,e,r){var n=dC(t,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(dC(e.reverse(),r).slice(1))},dI=function(t){var e=t.points,r=t.className,n=t.baseLinePoints,o=t.connectNulls,i=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,dP);if(!e||!e.length)return null;var a=(0,h.A)("recharts-polygon",r);if(n&&n.length){var c=i.stroke&&"none"!==i.stroke,u=dD(e,n,o);return y().createElement("g",{className:a},y().createElement("path",dE({},th(i,!0),{fill:"Z"===u.slice(-1)?i.fill:"none",stroke:"none",d:u})),c?y().createElement("path",dE({},th(i,!0),{fill:"none",d:dC(e,o)})):null,c?y().createElement("path",dE({},th(i,!0),{fill:"none",d:dC(n,o)})):null)}var l=dC(e,o);return y().createElement("path",dE({},th(i,!0),{fill:"Z"===l.slice(-1)?i.fill:"none",className:a,d:l}))};function dN(t){return(dN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dB(){return(dB=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dR(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dL(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dR(Object(r),!0).forEach(function(e){dq(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dR(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dz(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dW(n.key),n)}}function dU(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dU=function(){return!!t})()}function dF(t){return(dF=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function d$(t,e){return(d$=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dq(t,e,r){return(e=dW(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dW(t){var e=function(t,e){if("object"!=dN(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dN(e)?e:e+""}var dX=Math.PI/180,dH=function(t){var e,r;function n(){var t,e;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return t=n,e=arguments,t=dF(t),function(t,e){if(e&&("object"===dN(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,dU()?Reflect.construct(t,e||[],dF(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&d$(n,t),e=[{key:"getTickLineCoord",value:function(t){var e=this.props,r=e.cx,n=e.cy,o=e.radius,i=e.orientation,a=e.tickSize,c=lV(r,n,o,t.coordinate),u=lV(r,n,o+("inner"===i?-1:1)*(a||8),t.coordinate);return{x1:c.x,y1:c.y,x2:u.x,y2:u.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,r=Math.cos(-t.coordinate*dX);return r>1e-5?"outer"===e?"start":"end":r<-1e-5?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.radius,o=t.axisLine,i=t.axisLineType,a=dL(dL({},th(this.props,!1)),{},{fill:"none"},th(o,!1));if("circle"===i)return y().createElement(e6,dB({className:"recharts-polar-angle-axis-line"},a,{cx:e,cy:r,r:n}));var c=this.props.ticks.map(function(t){return lV(e,r,n,t.coordinate)});return y().createElement(dI,dB({className:"recharts-polar-angle-axis-line"},a,{points:c}))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,o=e.tick,i=e.tickLine,a=e.tickFormatter,c=e.stroke,u=th(this.props,!1),l=th(o,!1),s=dL(dL({},u),{},{fill:"none"},th(i,!1)),f=r.map(function(e,r){var f=t.getTickLineCoord(e),p=dL(dL(dL({textAnchor:t.getTickTextAnchor(e)},u),{},{stroke:"none",fill:c},l),{},{index:r,payload:e,x:f.x2,y:f.y2});return y().createElement(tD,dB({className:(0,h.A)("recharts-polar-angle-axis-tick",lQ(o)),key:"tick-".concat(e.coordinate)},Q(t.props,e,r)),i&&y().createElement("line",dB({className:"recharts-polar-angle-axis-tick-line"},s,f)),o&&n.renderTickItem(o,p,a?a(e.value,r):e.value))});return y().createElement(tD,{className:"recharts-polar-angle-axis-ticks"},f)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.radius,n=t.axisLine;return!(r<=0)&&e&&e.length?y().createElement(tD,{className:(0,h.A)("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return y().isValidElement(t)?y().cloneElement(t,e):$()(t)?t(e):y().createElement(n4,dB({},e,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],e&&dz(n.prototype,e),r&&dz(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(d.PureComponent);dq(dH,"displayName","PolarAngleAxis"),dq(dH,"axisType","angleAxis"),dq(dH,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var dV=r(57088),dG=r.n(dV),dY=r(10034),dK=r.n(dY),dZ=["cx","cy","angle","ticks","axisLine"],dJ=["ticks","tick","angle","tickFormatter","stroke"];function dQ(t){return(dQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d0(){return(d0=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function d1(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d1(Object(r),!0).forEach(function(e){d7(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d1(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d5(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function d3(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,d9(n.key),n)}}function d4(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(d4=function(){return!!t})()}function d6(t){return(d6=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function d8(t,e){return(d8=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function d7(t,e,r){return(e=d9(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d9(t){var e=function(t,e){if("object"!=dQ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dQ(e)?e:e+""}var yt=function(t){var e,r;function n(){var t,e;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return t=n,e=arguments,t=d6(t),function(t,e){if(e&&("object"===dQ(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,d4()?Reflect.construct(t,e||[],d6(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&d8(n,t),e=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,r=this.props,n=r.angle;return lV(r.cx,r.cy,e,n)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,i=dG()(o,function(t){return t.coordinate||0});return{cx:e,cy:r,startAngle:n,endAngle:n,innerRadius:dK()(o,function(t){return t.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,i=t.axisLine,a=d5(t,dZ),c=o.reduce(function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]},[1/0,-1/0]),u=lV(e,r,c[0],n),l=lV(e,r,c[1],n),s=d2(d2(d2({},th(a,!1)),{},{fill:"none"},th(i,!1)),{},{x1:u.x,y1:u.y,x2:l.x,y2:l.y});return y().createElement("line",d0({className:"recharts-polar-radius-axis-line"},s))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,o=e.tick,i=e.angle,a=e.tickFormatter,c=e.stroke,u=d5(e,dJ),l=this.getTickTextAnchor(),s=th(u,!1),f=th(o,!1),p=r.map(function(e,r){var u=t.getTickValueCoord(e),p=d2(d2(d2(d2({textAnchor:l,transform:"rotate(".concat(90-i,", ").concat(u.x,", ").concat(u.y,")")},s),{},{stroke:"none",fill:c},f),{},{index:r},u),{},{payload:e});return y().createElement(tD,d0({className:(0,h.A)("recharts-polar-radius-axis-tick",lQ(o)),key:"tick-".concat(e.coordinate)},Q(t.props,e,r)),n.renderTickItem(o,p,a?a(e.value,r):e.value))});return y().createElement(tD,{className:"recharts-polar-radius-axis-ticks"},p)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.axisLine,n=t.tick;return e&&e.length?y().createElement(tD,{className:(0,h.A)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),st.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return y().isValidElement(t)?y().cloneElement(t,e):$()(t)?t(e):y().createElement(n4,d0({},e,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],e&&d3(n.prototype,e),r&&d3(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(d.PureComponent);function ye(t){return(ye="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function yr(){return(yr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function yn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function yo(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?yn(Object(r),!0).forEach(function(e){yl(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):yn(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function yi(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ys(n.key),n)}}function ya(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ya=function(){return!!t})()}function yc(t){return(yc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function yu(t,e){return(yu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function yl(t,e,r){return(e=ys(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ys(t){var e=function(t,e){if("object"!=ye(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ye(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ye(e)?e:e+""}d7(yt,"displayName","PolarRadiusAxis"),d7(yt,"axisType","radiusAxis"),d7(yt,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var yf=function(t){var e,r;function n(t){var e,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[t],r=yc(r),yl(e=function(t,e){if(e&&("object"===ye(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ya()?Reflect.construct(r,o||[],yc(this).constructor):r.apply(this,o)),"pieRef",null),yl(e,"sectorRefs",[]),yl(e,"id",D("recharts-pie-")),yl(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),$()(t)&&t()}),yl(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),$()(t)&&t()}),e.state={isAnimationFinished:!t.isAnimationActive,prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,sectorToFocus:0},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&yu(n,t),e=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?0!==t.length:t||0===t}},{key:"renderLabels",value:function(t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var e=this.props,r=e.label,o=e.labelLine,i=e.dataKey,a=e.valueKey,c=th(this.props,!1),u=th(r,!1),l=th(o,!1),s=r&&r.offsetRadius||20,f=t.map(function(t,e){var f=(t.startAngle+t.endAngle)/2,p=lV(t.cx,t.cy,t.outerRadius+s,f),h=yo(yo(yo(yo({},c),t),{},{stroke:"none"},u),{},{index:e,textAnchor:n.getTextAnchor(p.x,t.cx)},p),d=yo(yo(yo(yo({},c),t),{},{fill:"none",stroke:t.fill},l),{},{index:e,points:[lV(t.cx,t.cy,t.outerRadius,f),p]}),v=i;return E()(i)&&E()(a)?v="value":E()(i)&&(v=a),y().createElement(tD,{key:"label-".concat(t.startAngle,"-").concat(t.endAngle,"-").concat(t.midAngle,"-").concat(e)},o&&n.renderLabelLineItem(o,d,"line"),n.renderLabelItem(r,h,uQ(t,v)))});return y().createElement(tD,{className:"recharts-pie-labels"},f)}},{key:"renderSectorsStatically",value:function(t){var e=this,r=this.props,n=r.activeShape,o=r.blendStroke,i=r.inactiveShape;return t.map(function(r,a){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==t.length)return null;var c=e.isActiveIndex(a),u=i&&e.hasActiveIndex()?i:null,l=yo(yo({},r),{},{stroke:o?r.fill:r.stroke,tabIndex:-1});return y().createElement(tD,yr({ref:function(t){t&&!e.sectorRefs.includes(t)&&e.sectorRefs.push(t)},tabIndex:-1,className:"recharts-pie-sector"},Q(e.props,r,a),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(a)}),y().createElement(sH,yr({option:c?n:u,isActive:c,shapeType:"sector"},l)))})}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,r=e.sectors,n=e.isAnimationActive,o=e.animationBegin,i=e.animationDuration,a=e.animationEasing,c=e.animationId,u=this.state,l=u.prevSectors,s=u.prevIsAnimationActive;return y().createElement(na,{begin:o,duration:i,isActive:n,easing:a,from:{t:0},to:{t:1},key:"pie-".concat(c,"-").concat(s),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(e){var n=e.t,o=[],i=(r&&r[0]).startAngle;return r.forEach(function(t,e){var r=l&&l[e],a=e>0?j()(t,"paddingAngle",0):0;if(r){var c=R(r.endAngle-r.startAngle,t.endAngle-t.startAngle),u=yo(yo({},t),{},{startAngle:i+a,endAngle:i+c(n)+a});o.push(u),i=u.endAngle}else{var s=R(0,t.endAngle-t.startAngle)(n),f=yo(yo({},t),{},{startAngle:i+a,endAngle:i+s+a});o.push(f),i=f.endAngle}}),y().createElement(tD,null,t.renderSectorsStatically(o))})}},{key:"attachKeyboardHandlers",value:function(t){var e=this;t.onkeydown=function(t){if(!t.altKey)switch(t.key){case"ArrowLeft":var r=++e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[r].focus(),e.setState({sectorToFocus:r});break;case"ArrowRight":var n=--e.state.sectorToFocus<0?e.sectorRefs.length-1:e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[n].focus(),e.setState({sectorToFocus:n});break;case"Escape":e.sectorRefs[e.state.sectorToFocus].blur(),e.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var t=this.props,e=t.sectors,r=t.isAnimationActive,n=this.state.prevSectors;return r&&e&&e.length&&(!n||!cK()(n,e))?this.renderSectorsWithAnimation():this.renderSectorsStatically(e)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var t=this,e=this.props,r=e.hide,n=e.sectors,o=e.className,i=e.label,a=e.cx,c=e.cy,u=e.innerRadius,l=e.outerRadius,s=e.isAnimationActive,f=this.state.isAnimationFinished;if(r||!n||!n.length||!T(a)||!T(c)||!T(u)||!T(l))return null;var p=(0,h.A)("recharts-pie",o);return y().createElement(tD,{tabIndex:this.props.rootTabIndex,className:p,ref:function(e){t.pieRef=e}},this.renderSectors(),i&&this.renderLabels(n),st.renderCallByParent(this.props,null,!1),(!s||f)&&sg.renderCallByParent(this.props,n,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return e.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:e.curSectors,isAnimationFinished:!0}:t.sectors!==e.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,e){return t>e?"start":t<e?"end":"middle"}},{key:"renderLabelLineItem",value:function(t,e,r){if(y().isValidElement(t))return y().cloneElement(t,e);if($()(t))return t(e);var n=(0,h.A)("recharts-pie-label-line","boolean"!=typeof t?t.className:"");return y().createElement(pJ,yr({},e,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(t,e,r){if(y().isValidElement(t))return y().cloneElement(t,e);var n=r;if($()(t)&&(n=t(e),y().isValidElement(n)))return n;var o=(0,h.A)("recharts-pie-label-text","boolean"==typeof t||$()(t)?"":t.className);return y().createElement(n4,yr({},e,{alignmentBaseline:"middle",className:o}),n)}}],e&&yi(n.prototype,e),r&&yi(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(d.PureComponent);yl(yf,"displayName","Pie"),yl(yf,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!t1.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),yl(yf,"parseDeltaAngle",function(t,e){return k(e-t)*Math.min(Math.abs(e-t),360)}),yl(yf,"getRealPieData",function(t){var e=t.data,r=t.children,n=th(t,!1),o=tu(r,sc);return e&&e.length?e.map(function(t,e){return yo(yo(yo({payload:t},n),t),o&&o[e]&&o[e].props)}):o&&o.length?o.map(function(t){return yo(yo({},n),t.props)}):[]}),yl(yf,"parseCoordinateOfPie",function(t,e){var r=e.top,n=e.left,o=e.width,i=e.height,a=lG(o,i);return{cx:n+I(t.cx,o,o/2),cy:r+I(t.cy,i,i/2),innerRadius:I(t.innerRadius,a,0),outerRadius:I(t.outerRadius,a,.8*a),maxRadius:t.maxRadius||Math.sqrt(o*o+i*i)/2}}),yl(yf,"getComposedData",function(t){var e,r,n=t.item,o=t.offset,i=void 0!==n.type.defaultProps?yo(yo({},n.type.defaultProps),n.props):n.props,a=yf.getRealPieData(i);if(!a||!a.length)return null;var c=i.cornerRadius,u=i.startAngle,l=i.endAngle,s=i.paddingAngle,f=i.dataKey,p=i.nameKey,h=i.valueKey,d=i.tooltipType,y=Math.abs(i.minAngle),v=yf.parseCoordinateOfPie(i,o),m=yf.parseDeltaAngle(u,l),b=Math.abs(m),g=f;E()(f)&&E()(h)?(U(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g="value"):E()(f)&&(U(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g=h);var x=a.filter(function(t){return 0!==uQ(t,g,0)}).length,O=b-x*y-(b>=360?x:x-1)*s,w=a.reduce(function(t,e){var r=uQ(e,g,0);return t+(T(r)?r:0)},0);return w>0&&(e=a.map(function(t,e){var n,o=uQ(t,g,0),i=uQ(t,p,e),a=(T(o)?o:0)/w,l=(n=e?r.endAngle+k(m)*s*(0!==o):u)+k(m)*((0!==o?y:0)+a*O),f=(n+l)/2,h=(v.innerRadius+v.outerRadius)/2,b=[{name:i,value:o,payload:t,dataKey:g,type:d}],x=lV(v.cx,v.cy,h,f);return r=yo(yo(yo({percent:a,cornerRadius:c,name:i,tooltipPayload:b,midAngle:f,middleRadius:h,tooltipPosition:x},t),v),{},{value:uQ(t,g),startAngle:n,endAngle:l,payload:t,paddingAngle:k(m)*s})})),yo(yo({},v),{},{sectors:e,data:a})});var yp=h_({chartName:"PieChart",GraphicalChild:yf,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:dH},{axisType:"radiusAxis",AxisComp:yt}],formatAxisMap:function(t,e,r,n,o){var i=t.width,a=t.height,c=t.startAngle,u=t.endAngle,l=I(t.cx,i,i/2),s=I(t.cy,a,a/2),f=lG(i,a,r),p=I(t.innerRadius,f,0),h=I(t.outerRadius,f,.8*f);return Object.keys(e).reduce(function(t,r){var i,a=e[r],f=a.domain,d=a.reversed;if(E()(a.range))"angleAxis"===n?i=[c,u]:"radiusAxis"===n&&(i=[p,h]),d&&(i=[i[1],i[0]]);else{var y,v=function(t){if(Array.isArray(t))return t}(y=i=a.range)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(y,2)||function(t,e){if(t){if("string"==typeof t)return lX(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lX(t,e)}}(y,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();c=v[0],u=v[1]}var m=lo(a,o),b=m.realScaleType,g=m.scale;g.domain(f).range(i),li(g);var x=lf(g,lq(lq({},a),{},{realScaleType:b})),O=lq(lq(lq({},a),x),{},{range:i,radius:h,realScaleType:b,scale:g,cx:l,cy:s,innerRadius:p,outerRadius:h,startAngle:c,endAngle:u});return lq(lq({},t),{},lW({},r,O))},{})},defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});function yh(){let t=(0,i.useTranslations)(),e=[{name:"الرواتب",value:400,color:"#0088FE"},{name:"الإيجار",value:300,color:"#00C49F"},{name:"المرافق",value:200,color:"#FFBB28"},{name:"أخرى",value:100,color:"#FF8042"}],r=[{title:t("dashboard.totalSales"),value:"125,000",change:"+12%",trend:"up",icon:a.A,color:"text-green-600",bgColor:"bg-green-100"},{title:t("dashboard.totalPurchases"),value:"85,000",change:"+8%",trend:"up",icon:c.A,color:"text-blue-600",bgColor:"bg-blue-100"},{title:t("dashboard.totalExpenses"),value:"45,000",change:"-5%",trend:"down",icon:u.A,color:"text-red-600",bgColor:"bg-red-100"},{title:t("dashboard.netProfit"),value:"35,000",change:"+15%",trend:"up",icon:s,color:"text-purple-600",bgColor:"bg-purple-100"}];return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"bg-gradient-to-r from-primary-600 to-primary-800 rounded-lg p-6 text-white",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-2",children:t("dashboard.welcome")}),(0,o.jsx)("p",{className:"text-primary-100",children:new Date().toLocaleDateString("ar-LY",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:r.map((t,e)=>{let r=t.icon;return(0,o.jsx)("div",{className:"card",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-sm font-medium text-gray-600",children:t.title}),(0,o.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:t.value}),(0,o.jsxs)("div",{className:"flex items-center mt-2",children:["up"===t.trend?(0,o.jsx)(f,{className:"w-4 h-4 text-green-500 mr-1"}):(0,o.jsx)(p,{className:"w-4 h-4 text-red-500 mr-1"}),(0,o.jsx)("span",{className:`text-sm font-medium ${"up"===t.trend?"text-green-600":"text-red-600"}`,children:t.change})]})]}),(0,o.jsx)("div",{className:`p-3 rounded-full ${t.bgColor}`,children:(0,o.jsx)(r,{className:`w-6 h-6 ${t.color}`})})]})},e)})}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{className:"card",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:t("dashboard.monthlyComparison")}),(0,o.jsx)(tw,{width:"100%",height:300,children:(0,o.jsxs)(du,{data:[{month:"يناير",sales:4e3,expenses:2400},{month:"فبراير",sales:3e3,expenses:1398},{month:"مارس",sales:2e3,expenses:9800},{month:"أبريل",sales:2780,expenses:3908},{month:"مايو",sales:1890,expenses:4800},{month:"يونيو",sales:2390,expenses:3800}],children:[(0,o.jsx)(dA,{strokeDasharray:"3 3"}),(0,o.jsx)(h8,{dataKey:"month"}),(0,o.jsx)(dc,{}),(0,o.jsx)(eo,{}),(0,o.jsx)(fu,{dataKey:"sales",fill:"#3b82f6",name:"المبيعات"}),(0,o.jsx)(fu,{dataKey:"expenses",fill:"#ef4444",name:"المصروفات"})]})})]}),(0,o.jsxs)("div",{className:"card",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:t("dashboard.expensesChart")}),(0,o.jsx)(tw,{width:"100%",height:300,children:(0,o.jsxs)(yp,{children:[(0,o.jsx)(yf,{data:e,cx:"50%",cy:"50%",labelLine:!1,label:({name:t,percent:e})=>`${t} ${(100*e).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:e.map((t,e)=>(0,o.jsx)(sc,{fill:t.color},`cell-${e}`))}),(0,o.jsx)(eo,{})]})})]})]}),(0,o.jsxs)("div",{className:"card",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:t("dashboard.recentTransactions")}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,o.jsx)("thead",{className:"bg-gray-50",children:(0,o.jsxs)("tr",{children:[(0,o.jsx)("th",{className:"table-header px-6 py-3 text-right",children:t("common.description")}),(0,o.jsx)("th",{className:"table-header px-6 py-3 text-right",children:t("common.amount")}),(0,o.jsx)("th",{className:"table-header px-6 py-3 text-right",children:t("common.type")}),(0,o.jsx)("th",{className:"table-header px-6 py-3 text-right",children:t("common.date")})]})}),(0,o.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:[{id:1,description:"مبيعات نقدية",amount:1500,type:"credit",date:"2024-01-15"},{id:2,description:"دفع إيجار",amount:800,type:"debit",date:"2024-01-14"},{id:3,description:"شراء معدات",amount:2500,type:"debit",date:"2024-01-13"},{id:4,description:"إيرادات خدمات",amount:1200,type:"credit",date:"2024-01-12"}].map(t=>(0,o.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,o.jsx)("td",{className:"table-cell",children:t.description}),(0,o.jsx)("td",{className:"table-cell",children:(0,o.jsxs)("span",{className:`font-medium ${"credit"===t.type?"text-green-600":"text-red-600"}`,children:["credit"===t.type?"+":"-",t.amount.toLocaleString()," د.ل"]})}),(0,o.jsx)("td",{className:"table-cell",children:(0,o.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"credit"===t.type?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:"credit"===t.type?"دائن":"مدين"})}),(0,o.jsx)("td",{className:"table-cell",children:new Date(t.date).toLocaleDateString("ar-LY")})]},t.id))})]})})]})]})}},15871:(t,e,r)=>{var n=r(36341),o=r(27467);t.exports=function t(e,r,i,a,c){return e===r||(null!=e&&null!=r&&(o(e)||o(r))?n(e,r,i,a,t,c):e!=e&&r!=r)}},15883:(t,e,r)=>{var n=r(2984),o=r(46063),i=r(48169);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},15909:(t,e,r)=>{var n=r(87506),o=r(66930),i=r(658);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},16854:t=>{t.exports=function(t){return this.__data__.has(t)}},17518:(t,e,r)=>{var n=r(21367),o=r(1707),i=r(22),a=r(54765),c=r(43378),u=r(89624),l=r(65727),s=r(48169),f=r(40542);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,u(i)),c(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},17830:(t,e,r)=>{t.exports=r(41547)(r(85718),"WeakMap")},18234:(t,e,r)=>{var n=r(91290),o=r(22),i=r(84482),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:i(r);return u<0&&(u=a(c+u,0)),n(t,o(e,3),u)}},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(t,e,r)=>{var n=r(8336);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=+(r.size!=o),this}},20540:(t,e,r)=>{var n=r(55048),o=r(70151),i=r(11539),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,i=o();if(g(i))return O(i);p=setTimeout(x,(t=i-h,r=i-d,n=e-t,v?c(n,s-r):n))}function O(t){return(p=void 0,m&&u)?b(t):(u=l=void 0,f)}function w(){var t,r=o(),n=g(r);if(u=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),w.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=l=p=void 0},w.flush=function(){return void 0===p?f:O(o())},w}},20623:(t,e,r)=>{var n=r(15871),o=r(40491),i=r(2896),a=r(67619),c=r(34883),u=r(41132),l=r(46436);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},21367:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},21456:(t,e,r)=>{var n=r(41693),o=r(40542);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},21592:(t,e,r)=>{var n=r(42205),o=r(61837);t.exports=function(t,e){return n(o(t,e),1)}},21630:(t,e,r)=>{var n=r(10653),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:o.call(e,p)))return!1}var h=c.get(t),d=c.get(e);if(h&&d)return h==e&&d==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=u?i(b,m,p,e,t,c):i(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,i,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,O=e.constructor;x!=O&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof O&&O instanceof O)&&(y=!1)}return c.delete(t),c.delete(e),y}},22964:(t,e,r)=>{t.exports=r(23729)(r(18234))},23729:(t,e,r)=>{var n=r(22),o=r(32269),i=r(7651);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!o(e)){var u=n(r,3);e=i(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},25118:t=>{t.exports=function(t){return this.__data__.has(t)}},27006:(t,e,r)=>{var n=r(46328),o=r(99525),i=r(58276);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,u):a(m,b,d,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},27467:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},27669:t=>{t.exports=function(){this.__data__=[],this.size=0}},28837:(t,e,r)=>{var n=r(57797),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},28977:(t,e,r)=>{var n=r(11539),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},29205:(t,e,r)=>{var n=r(8336);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=!!e,e}},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(t,e,r)=>{var n=r(79474),o=r(70222),i=r(84713),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},29508:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).get(t)}},30316:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},30401:(t,e,r)=>{t.exports=r(41547)(r(85718),"Promise")},30854:(t,e,r)=>{var n=r(66930),o=r(658),i=r(95746);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},32269:(t,e,r)=>{var n=r(5231),o=r(69619);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},33873:t=>{"use strict";t.exports=require("path")},34117:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},34452:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34746:t=>{t.exports=function(t){return this.__data__.get(t)}},34772:(t,e,r)=>{t.exports=r(41547)(r(85718),"Set")},34821:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},34883:(t,e,r)=>{var n=r(55048);t.exports=function(t){return t==t&&!n(t)}},34990:(t,e,r)=>{t.exports=r(87321)()},35142:(t,e,r)=>{var n=r(40542),o=r(67619),i=r(51449),a=r(42403);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},35163:(t,e,r)=>{var n=r(15451),o=r(27467),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable;t.exports=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!c.call(t,"callee")}},35697:(t,e,r)=>{var n=r(79474),o=r(4999),i=r(67009),a=r(27006),c=r(59774),u=r(2408),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new o(t),new o(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var d=1&n;if(h||(h=u),t.size!=e.size&&!d)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},35800:(t,e,r)=>{var n=r(57797);t.exports=function(t){return n(this.__data__,t)>-1}},36315:(t,e,r)=>{var n=r(22),o=r(92662);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},36341:(t,e,r)=>{var n=r(67200),o=r(27006),i=r(35697),a=r(21630),c=r(1566),u=r(40542),l=r(80329),s=r(10090),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),O=g?p:c(e);x=x==f?h:x,O=O==f?h:O;var w=x==h,j=O==h,S=x==O;if(S&&l(t)){if(!l(e))return!1;b=!0,w=!1}if(S&&!w)return m||(m=new n),b||s(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var A=w&&d.call(t,"__wrapped__"),P=j&&d.call(e,"__wrapped__");if(A||P){var E=A?t.value():t,k=P?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},36959:t=>{t.exports=function(){}},37456:t=>{t.exports=function(t){return null==t}},37575:(t,e,r)=>{var n=r(66930);t.exports=function(){this.__data__=new n,this.size=0}},37643:(t,e,r)=>{var n=r(6053),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},38404:(t,e,r)=>{var n=r(29395),o=r(65932),i=r(27467),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,l=c.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},38428:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},39672:(t,e,r)=>{var n=r(58141);t.exports=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},39774:t=>{t.exports=function(t){return t!=t}},40491:(t,e,r)=>{var n=r(1707);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},40542:t=>{t.exports=Array.isArray},41011:(t,e,r)=>{var n=r(41353);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},41132:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},41157:(t,e,r)=>{var n=r(91928);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},41353:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},41547:(t,e,r)=>{var n=r(61548),o=r(90851);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},41693:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},42082:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},42205:(t,e,r)=>{var n=r(41693),o=r(85450);t.exports=function t(e,r,i,a,c){var u=-1,l=e.length;for(i||(i=o),c||(c=[]);++u<l;){var s=e[u];r>0&&i(s)?r>1?t(s,r-1,i,a,c):n(c,s):a||(c[c.length]=s)}return c}},42403:(t,e,r)=>{var n=r(80195);t.exports=function(t){return null==t?"":n(t)}},43378:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},45058:(t,e,r)=>{var n=r(42082),o=r(8852),i=r(67619),a=r(46436);t.exports=function(t){return i(t)?n(a(t)):o(t)}},45603:(t,e,r)=>{var n=r(20540),o=r(55048);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},45803:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},46063:t=>{t.exports=function(t,e){return t<e}},46229:(t,e,r)=>{var n=r(48169),o=r(66354),i=r(11424);t.exports=function(t,e){return i(o(t,e,n),t+"")}},46328:(t,e,r)=>{var n=r(95746),o=r(89185),i=r(16854);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},46436:(t,e,r)=>{var n=r(49227),o=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},47212:(t,e,r)=>{var n=r(87270),o=r(30316),i=r(22),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},47282:(t,e,r)=>{t=r.nmd(t);var n=r(10663),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,c=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},47603:(t,e,r)=>{var n=r(14675),o=r(91928),i=r(48169);t.exports=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i},48169:t=>{t.exports=function(t){return t}},48385:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+u+c+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+e+"]"].join("|"))+")"+(u+c+l),"g");t.exports=function(t){return t.match(s)||[]}},49227:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},50684:(t,e,r)=>{Promise.resolve().then(r.bind(r,15529))},51449:(t,e,r)=>{var n=r(85745),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;t.exports=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e})},52599:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},52823:(t,e,r)=>{var n=r(85406),o=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!o&&o in t}},52931:(t,e,r)=>{var n=r(77834),o=r(89605),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},54765:(t,e,r)=>{var n=r(67554),o=r(32269);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},55048:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},56506:(t,e,r)=>{var n=r(32269);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,c=Object(r);(e?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},57088:(t,e,r)=>{var n=r(2984),o=r(99180),i=r(22);t.exports=function(t,e){return t&&t.length?n(t,i(e,2),o):void 0}},57797:(t,e,r)=>{var n=r(67009);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},58001:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\(dashboard)\\page.tsx","default")},58141:(t,e,r)=>{t.exports=r(41547)(Object,"create")},58276:t=>{t.exports=function(t,e){return t.has(e)}},58744:(t,e,r)=>{var n=r(57797);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},59467:(t,e,r)=>{var n=r(35142),o=r(35163),i=r(40542),a=r(38428),c=r(69619),u=r(46436);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(i(t)||o(t))}},59774:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},61320:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).has(t)}},61548:(t,e,r)=>{var n=r(5231),o=r(52823),i=r(55048),a=r(12290),c=/^\[object .+?Constructor\]$/,u=Object.prototype,l=Function.prototype.toString,s=u.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?f:c).test(a(t))}},61837:(t,e,r)=>{var n=r(21367),o=r(22),i=r(54765),a=r(40542);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(t,e,r)=>{var n=r(29395),o=r(40542),i=r(27467);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},63979:(t,e,r)=>{var n=r(52599),o=r(6330),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return i.call(t,e)})}:o},65662:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},65727:(t,e,r)=>{var n=r(81957);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,c=i.length,u=r.length;++o<c;){var l=n(i[o],a[o]);if(l){if(o>=u)return l;return l*("desc"==r[o]?-1:1)}}return t.index-e.index}},65932:(t,e,r)=>{t.exports=r(65662)(Object.getPrototypeOf,Object)},65984:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}},66354:(t,e,r)=>{var n=r(85244),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,c=o(i.length-e,0),u=Array(c);++a<c;)u[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(u),n(t,this,l)}}},66400:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},66713:(t,e,r)=>{var n=r(3105),o=r(34117),i=r(48385);t.exports=function(t){return o(t)?i(t):n(t)}},66837:(t,e,r)=>{var n=r(58141);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(t,e,r)=>{var n=r(27669),o=r(28837),i=r(94388),a=r(35800),c=r(58744);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},66992:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case i:case c:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case d:case u:return t;default:return e}}case o:return e}}}(t)===i}},67009:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},67200:(t,e,r)=>{var n=r(66930),o=r(37575),i=r(75411),a=r(34746),c=r(25118),u=r(30854);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},67367:(t,e,r)=>{var n=r(99525),o=r(22),i=r(75847),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},67554:(t,e,r)=>{var n=r(99114);t.exports=r(56506)(n)},67619:(t,e,r)=>{var n=r(40542),o=r(49227),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},69433:(t,e,r)=>{t.exports=r(5566)("toUpperCase")},69619:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}},69691:(t,e,r)=>{var n=r(41157),o=r(99114),i=r(22);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},70151:(t,e,r)=>{var n=r(85718);t.exports=function(){return n.Date.now()}},70222:(t,e,r)=>{var n=r(79474),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[c]=r:delete t[c]),o}},71960:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},71967:(t,e,r)=>{var n=r(15871);t.exports=function(t,e){return n(t,e)}},74610:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return -1}},75254:(t,e,r)=>{var n=r(78418),o=r(93311),i=r(41132);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},75411:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},75847:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},77822:(t,e,r)=>{var n=r(93490);t.exports=function(t){return n(t)&&t!=+t}},77834:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},78418:(t,e,r)=>{var n=r(67200),o=r(15871);t.exports=function(t,e,r,i){var a=r.length,c=a,u=!i;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(i)var d=i(f,p,s,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},79474:(t,e,r)=>{t.exports=r(85718).Symbol},80195:(t,e,r)=>{var n=r(79474),o=r(21367),i=r(40542),a=r(49227),c=1/0,u=n?n.prototype:void 0,l=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},80329:(t,e,r)=>{t=r.nmd(t);var n=r(85718),o=r(1944),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,c=a&&a.exports===i?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||o},80458:(t,e,r)=>{var n=r(29395),o=r(69619),i=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},80704:(t,e,r)=>{var n=r(96678);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},81488:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},81957:(t,e,r)=>{var n=r(49227);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),c=void 0!==e,u=null===e,l=e==e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||o&&c&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||u&&r&&i||!c&&i||!l)return -1}return 0}},82038:(t,e,r)=>{var n=r(34821),o=r(35163),i=r(40542),a=r(80329),c=r(38428),u=r(10090),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)(e||l.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&d.push(v);return d}},84031:(t,e,r)=>{"use strict";var n=r(34452);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},84261:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e}},84482:(t,e,r)=>{var n=r(28977);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},84713:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},85244:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},85406:(t,e,r)=>{t.exports=r(85718)["__core-js_shared__"]},85450:(t,e,r)=>{var n=r(79474),o=r(35163),i=r(40542),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},85718:(t,e,r)=>{var n=r(10663),o="object"==typeof self&&self&&self.Object===Object&&self;t.exports=n||o||Function("return this")()},85745:(t,e,r)=>{var n=r(86451);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},85873:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>s,routeModule:()=>p,tree:()=>l});var n=r(65239),o=r(48088),i=r(88170),a=r.n(i),c=r(30893),u={};for(let t in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(u[t]=()=>c[t]);r.d(e,u);let l={children:["",{children:["[locale]",{children:["(dashboard)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58001)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\(dashboard)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,55338)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\(dashboard)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,11434)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,66196)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\error.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,s=["C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\(dashboard)\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/(dashboard)/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},85938:(t,e,r)=>{var n=r(42205),o=r(17518),i=r(46229),a=r(7383);t.exports=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])})},86451:(t,e,r)=>{var n=r(95746);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},87270:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},87321:(t,e,r)=>{var n=r(98798),o=r(7383),i=r(28977);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},87506:(t,e,r)=>{var n=r(66837),o=r(84261),i=r(89492),a=r(90200),c=r(39672);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},87955:(t,e,r)=>{t.exports=r(84031)()},89167:(t,e,r)=>{t.exports=r(41547)(r(85718),"DataView")},89185:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},89492:(t,e,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},89605:(t,e,r)=>{t.exports=r(65662)(Object.keys,Object)},89624:t=>{t.exports=function(t){return function(e){return t(e)}}},90200:(t,e,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},90453:(t,e,r)=>{var n=r(2984),o=r(99180),i=r(48169);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},90851:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},91290:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},91928:(t,e,r)=>{var n=r(41547);t.exports=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},92540:(t,e,r)=>{Promise.resolve().then(r.bind(r,58001))},92662:(t,e,r)=>{var n=r(46328),o=r(80704),i=r(71960),a=r(58276),c=r(95308),u=r(2408);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=i;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},93311:(t,e,r)=>{var n=r(34883),o=r(7651);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},93490:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},93780:(t,e,r)=>{"use strict";t.exports=r(66992)},94388:(t,e,r)=>{var n=r(57797);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},95308:(t,e,r)=>{var n=r(34772),o=r(36959),i=r(2408);t.exports=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o},95746:(t,e,r)=>{var n=r(15909),o=r(29205),i=r(29508),a=r(61320),c=r(19976);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},96678:(t,e,r)=>{var n=r(91290),o=r(39774),i=r(74610);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},98451:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},98798:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,c=r(e((n-t)/(o||1)),0),u=Array(c);c--;)u[i?c:++a]=t,t+=o;return u}},99114:(t,e,r)=>{var n=r(12344),o=r(7651);t.exports=function(t,e){return t&&n(t,e,o)}},99180:t=>{t.exports=function(t,e){return t>e}},99525:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}}};var e=require("../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[447,756,207,87],()=>r(85873));module.exports=n})();