"use strict";exports.id=368,exports.ids=[368],exports.modules={87368:e=>{e.exports=JSON.parse('{"common":{"save":"Save","cancel":"Cancel","delete":"Delete","edit":"Edit","add":"Add","search":"Search","loading":"Loading...","error":"Error","success":"Success","confirm":"Confirm","yes":"Yes","no":"No","close":"Close","print":"Print","export":"Export","import":"Import","total":"Total","date":"Date","description":"Description","amount":"Amount","balance":"Balance","code":"Code","name":"Name","type":"Type","status":"Status","active":"Active","inactive":"Inactive"},"navigation":{"dashboard":"Dashboard","accounts":"Accounts","sales":"Sales","purchases":"Purchases","inventory":"Inventory","payroll":"Payroll","payments":"Payments","receipts":"Receipts","reports":"Reports","settings":"Settings"},"dashboard":{"title":"Dashboard","welcome":"Welcome to Smart ERP System","totalSales":"Total Sales","totalPurchases":"Total Purchases","totalExpenses":"Total Expenses","netProfit":"Net Profit","cashFlow":"Cash Flow","accountsReceivable":"Accounts Receivable","accountsPayable":"Accounts Payable","inventory":"Inventory","salesChart":"Sales Chart","expensesChart":"Expenses Chart","monthlyComparison":"Monthly Comparison","recentTransactions":"Recent Transactions"},"accounts":{"title":"Accounts","chartOfAccounts":"Chart of Accounts","journalEntries":"Journal Entries","financialStatements":"Financial Statements","trialBalance":"Trial Balance","incomeStatement":"Income Statement","balanceSheet":"Balance Sheet","addAccount":"Add Account","editAccount":"Edit Account","deleteAccount":"Delete Account","accountCode":"Account Code","accountName":"Account Name","accountType":"Account Type","parentAccount":"Parent Account","level":"Level","debit":"Debit","credit":"Credit","journalNumber":"Journal Number","reference":"Reference","addJournalEntry":"Add Journal Entry","postEntry":"Post Entry","unpostEntry":"Unpost Entry"},"accountTypes":{"ASSET":"Asset","LIABILITY":"Liability","EQUITY":"Equity","REVENUE":"Revenue","EXPENSE":"Expense"},"modules":{"sales":{"title":"Sales","underDevelopment":"This module is under development","comingSoon":"Coming Soon..."},"purchases":{"title":"Purchases","underDevelopment":"This module is under development","comingSoon":"Coming Soon..."},"inventory":{"title":"Inventory","underDevelopment":"This module is under development","comingSoon":"Coming Soon..."},"payroll":{"title":"Payroll","underDevelopment":"This module is under development","comingSoon":"Coming Soon..."},"payments":{"title":"Payments","underDevelopment":"This module is under development","comingSoon":"Coming Soon..."},"receipts":{"title":"Receipts","underDevelopment":"This module is under development","comingSoon":"Coming Soon..."}},"settings":{"title":"Settings","companyInfo":"Company Information","companyName":"Company Name","companyAddress":"Company Address","companyPhone":"Company Phone","companyEmail":"Company Email","companyLogo":"Company Logo","language":"Language","currency":"Currency","currencies":{"LYD":"Libyan Dinar","EGP":"Egyptian Pound","SAR":"Saudi Riyal"}},"errors":{"required":"This field is required","invalidEmail":"Invalid email address","invalidNumber":"Invalid number","minLength":"Minimum {min} characters","maxLength":"Maximum {max} characters","accountCodeExists":"Account code already exists","cannotDeleteAccount":"Cannot delete account with transactions"}}')}};