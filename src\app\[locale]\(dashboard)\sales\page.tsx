'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  FileText, 
  Users, 
  Settings, 
  BarChart3, 
  Plus,
  Receipt,
  CreditCard,
  Package
} from 'lucide-react';

export default function SalesPage() {
  const t = useTranslations('sales');
  const params = useParams();
  const locale = params.locale as string;

  const modules = [
    {
      title: t('invoices'),
      description: 'إدارة فواتير المبيعات',
      href: `/${locale}/sales/invoices`,
      icon: FileText,
      color: 'bg-blue-500',
      features: ['إنشاء فواتير', 'إدارة الحالات', 'الترحيل المحاسبي', 'طباعة التقارير']
    },
    {
      title: t('customers'),
      description: 'إدارة بيانات العملاء',
      href: `/${locale}/sales/customers`,
      icon: Users,
      color: 'bg-green-500',
      features: ['إضافة عملاء', 'كشف الحسابات', 'متابعة المديونية', 'تقارير العملاء']
    },
    {
      title: t('services'),
      description: 'إدارة الخدمات والأسعار',
      href: `/${locale}/sales/services`,
      icon: Package,
      color: 'bg-purple-500',
      features: ['إضافة خدمات', 'تحديث الأسعار', 'تقارير الخدمات', 'إحصائيات المبيعات']
    },
    {
      title: t('reports'),
      description: 'التقارير والإحصائيات',
      href: `/${locale}/sales/reports`,
      icon: BarChart3,
      color: 'bg-orange-500',
      features: ['تقرير المبيعات', 'تقرير العملاء', 'تقرير الخدمات', 'تحليلات متقدمة']
    },
    {
      title: t('settings'),
      description: 'إعدادات المبيعات',
      href: `/${locale}/sales/settings`,
      icon: Settings,
      color: 'bg-gray-500',
      features: ['إعدادات القيود', 'ترقيم الفواتير', 'الضريبة', 'الخصومات']
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {t('title')}
        </h1>
        <p className="text-gray-600">
          نظام إدارة المبيعات المتكامل مع الترحيل المحاسبي التلقائي
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {modules.map((module, index) => (
          <Link
            key={index}
            href={module.href}
            className="group block bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 border border-gray-200 hover:border-blue-300"
          >
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className={`p-3 rounded-lg ${module.color} text-white mr-4`}>
                  <module.icon className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {module.title}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {module.description}
                  </p>
                </div>
              </div>
              
              <ul className="space-y-2">
                {module.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></div>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          </Link>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="mt-12 bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">إجراءات سريعة</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            href={`/${locale}/sales/invoices/create`}
            className="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
          >
            <Plus className="w-5 h-5 text-blue-600 mr-3" />
            <span className="font-medium text-blue-900">إنشاء فاتورة جديدة</span>
          </Link>
          
          <Link
            href={`/${locale}/sales/customers/create`}
            className="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
          >
            <Users className="w-5 h-5 text-green-600 mr-3" />
            <span className="font-medium text-green-900">إضافة عميل جديد</span>
          </Link>
          
          <Link
            href={`/${locale}/sales/services/create`}
            className="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
          >
            <Package className="w-5 h-5 text-purple-600 mr-3" />
            <span className="font-medium text-purple-900">إضافة خدمة جديدة</span>
          </Link>
        </div>
      </div>
    </div>
  );
}
