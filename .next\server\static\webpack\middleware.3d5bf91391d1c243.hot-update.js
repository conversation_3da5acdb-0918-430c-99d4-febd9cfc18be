"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/middleware */ \"(middleware)/./node_modules/next-intl/dist/development/middleware.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_middleware__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    // A list of all locales that are supported\n    locales: [\n        'ar',\n        'en'\n    ],\n    // Used when no locale matches\n    defaultLocale: 'ar',\n    // Always use locale prefix\n    localePrefix: 'always'\n}));\nconst config = {\n    // Match only internationalized pathnames\n    matcher: [\n        '/((?!api|_next/static|_next/image|favicon.ico).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL21pZGRsZXdhcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9EO0FBRXBELGlFQUFlQSxnRUFBZ0JBLENBQUM7SUFDOUIsMkNBQTJDO0lBQzNDQyxTQUFTO1FBQUM7UUFBTTtLQUFLO0lBRXJCLDhCQUE4QjtJQUM5QkMsZUFBZTtJQUVmLDJCQUEyQjtJQUMzQkMsY0FBYztBQUNoQixFQUFFLEVBQUM7QUFFSSxNQUFNQyxTQUFTO0lBQ3BCLHlDQUF5QztJQUN6Q0MsU0FBUztRQUFDO0tBQW9EO0FBQ2hFLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaG9zc2FcXERvd25sb2Fkc1xcU01BUlRcXHNyY1xcbWlkZGxld2FyZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTWlkZGxld2FyZSBmcm9tICduZXh0LWludGwvbWlkZGxld2FyZSc7XG5cbmV4cG9ydCBkZWZhdWx0IGNyZWF0ZU1pZGRsZXdhcmUoe1xuICAvLyBBIGxpc3Qgb2YgYWxsIGxvY2FsZXMgdGhhdCBhcmUgc3VwcG9ydGVkXG4gIGxvY2FsZXM6IFsnYXInLCAnZW4nXSxcblxuICAvLyBVc2VkIHdoZW4gbm8gbG9jYWxlIG1hdGNoZXNcbiAgZGVmYXVsdExvY2FsZTogJ2FyJyxcblxuICAvLyBBbHdheXMgdXNlIGxvY2FsZSBwcmVmaXhcbiAgbG9jYWxlUHJlZml4OiAnYWx3YXlzJ1xufSk7XG5cbmV4cG9ydCBjb25zdCBjb25maWcgPSB7XG4gIC8vIE1hdGNoIG9ubHkgaW50ZXJuYXRpb25hbGl6ZWQgcGF0aG5hbWVzXG4gIG1hdGNoZXI6IFsnLygoPyFhcGl8X25leHQvc3RhdGljfF9uZXh0L2ltYWdlfGZhdmljb24uaWNvKS4qKSddXG59O1xuIl0sIm5hbWVzIjpbImNyZWF0ZU1pZGRsZXdhcmUiLCJsb2NhbGVzIiwiZGVmYXVsdExvY2FsZSIsImxvY2FsZVByZWZpeCIsImNvbmZpZyIsIm1hdGNoZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});