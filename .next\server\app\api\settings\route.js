(()=>{var e={};e.id=177,e.ids=[177],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},58650:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>d,PUT:()=>p});var n=r(96559),o=r(48088),a=r(37719),i=r(32190),u=r(94747);async function d(){try{let e=await u.z.companySettings.findFirst({where:{id:"default"}});if(!e)return i.NextResponse.json({id:"default",name:"شركة سمارت للحلول المحاسبية",nameEn:"Smart Accounting Solutions Company",address:"",addressEn:"",phone:"",email:"",website:"",logo:null,currency:"LYD",language:"ar"});return i.NextResponse.json(e)}catch(e){return console.error("Error fetching settings:",e),i.NextResponse.json({error:"Failed to fetch settings"},{status:500})}}async function p(e){try{let{name:t,nameEn:r,address:s,addressEn:n,phone:o,email:a,website:d,logo:p,currency:c,language:l}=await e.json(),g=await u.z.companySettings.upsert({where:{id:"default"},update:{...void 0!==t&&{name:t},...void 0!==r&&{nameEn:r},...void 0!==s&&{address:s},...void 0!==n&&{addressEn:n},...void 0!==o&&{phone:o},...void 0!==a&&{email:a},...void 0!==d&&{website:d},...void 0!==p&&{logo:p},...void 0!==c&&{currency:c},...void 0!==l&&{language:l}},create:{id:"default",name:t||"شركة سمارت للحلول المحاسبية",nameEn:r||"Smart Accounting Solutions Company",address:s||"",addressEn:n||"",phone:o||"",email:a||"",website:d||"",logo:p||null,currency:c||"LYD",language:l||"ar"}});return i.NextResponse.json(g)}catch(e){return console.error("Error updating settings:",e),i.NextResponse.json({error:"Failed to update settings"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/settings/route",pathname:"/api/settings",filename:"route",bundlePath:"app/api/settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\api\\settings\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:m}=c;function v(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},94747:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});let s=require("@prisma/client"),n=globalThis.prisma??new s.PrismaClient},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(58650));module.exports=s})();