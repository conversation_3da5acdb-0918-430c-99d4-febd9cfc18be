import { PrismaClient, AccountType } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Create company settings
  await prisma.companySettings.upsert({
    where: { id: 'default' },
    update: {},
    create: {
      id: 'default',
      name: 'شركة سمارت للحلول المحاسبية',
      nameEn: 'Smart Accounting Solutions Company',
      address: 'طرابلس، ليبيا',
      addressEn: 'Tripoli, Libya',
      phone: '+218-21-1234567',
      email: '<EMAIL>',
      currency: 'LYD',
      language: 'ar',
    },
  });

  // Create main account categories
  const assets = await prisma.account.upsert({
    where: { code: '1000' },
    update: {},
    create: {
      code: '1000',
      name: 'الأصول',
      nameEn: 'Assets',
      type: AccountType.ASSET,
      level: 1,
    },
  });

  const liabilities = await prisma.account.upsert({
    where: { code: '2000' },
    update: {},
    create: {
      code: '2000',
      name: 'الخصوم',
      nameEn: 'Liabilities',
      type: AccountType.LIABILITY,
      level: 1,
    },
  });

  const equity = await prisma.account.upsert({
    where: { code: '3000' },
    update: {},
    create: {
      code: '3000',
      name: 'حقوق الملكية',
      nameEn: 'Equity',
      type: AccountType.EQUITY,
      level: 1,
    },
  });

  const revenue = await prisma.account.upsert({
    where: { code: '4000' },
    update: {},
    create: {
      code: '4000',
      name: 'الإيرادات',
      nameEn: 'Revenue',
      type: AccountType.REVENUE,
      level: 1,
    },
  });

  const expenses = await prisma.account.upsert({
    where: { code: '5000' },
    update: {},
    create: {
      code: '5000',
      name: 'المصروفات',
      nameEn: 'Expenses',
      type: AccountType.EXPENSE,
      level: 1,
    },
  });

  // Create sub-accounts for Assets
  await prisma.account.createMany({
    data: [
      // Current Assets
      {
        code: '1100',
        name: 'الأصول المتداولة',
        nameEn: 'Current Assets',
        type: AccountType.ASSET,
        parentId: assets.id,
        level: 2,
      },
      {
        code: '1110',
        name: 'النقدية والبنوك',
        nameEn: 'Cash and Banks',
        type: AccountType.ASSET,
        level: 3,
      },
      {
        code: '1120',
        name: 'العملاء',
        nameEn: 'Accounts Receivable',
        type: AccountType.ASSET,
        level: 3,
      },
      {
        code: '1130',
        name: 'المخزون',
        nameEn: 'Inventory',
        type: AccountType.ASSET,
        level: 3,
      },
      // Fixed Assets
      {
        code: '1200',
        name: 'الأصول الثابتة',
        nameEn: 'Fixed Assets',
        type: AccountType.ASSET,
        parentId: assets.id,
        level: 2,
      },
      {
        code: '1210',
        name: 'الأراضي والمباني',
        nameEn: 'Land and Buildings',
        type: AccountType.ASSET,
        level: 3,
      },
      {
        code: '1220',
        name: 'المعدات والآلات',
        nameEn: 'Equipment and Machinery',
        type: AccountType.ASSET,
        level: 3,
      },
    ],
    skipDuplicates: true,
  });

  // Create sub-accounts for Liabilities
  await prisma.account.createMany({
    data: [
      // Current Liabilities
      {
        code: '2100',
        name: 'الخصوم المتداولة',
        nameEn: 'Current Liabilities',
        type: AccountType.LIABILITY,
        parentId: liabilities.id,
        level: 2,
      },
      {
        code: '2110',
        name: 'الموردون',
        nameEn: 'Accounts Payable',
        type: AccountType.LIABILITY,
        level: 3,
      },
      {
        code: '2120',
        name: 'رواتب مستحقة',
        nameEn: 'Accrued Salaries',
        type: AccountType.LIABILITY,
        level: 3,
      },
      // Long-term Liabilities
      {
        code: '2200',
        name: 'الخصوم طويلة الأجل',
        nameEn: 'Long-term Liabilities',
        type: AccountType.LIABILITY,
        parentId: liabilities.id,
        level: 2,
      },
    ],
    skipDuplicates: true,
  });

  // Create sub-accounts for Equity
  await prisma.account.createMany({
    data: [
      {
        code: '3100',
        name: 'رأس المال',
        nameEn: 'Capital',
        type: AccountType.EQUITY,
        parentId: equity.id,
        level: 2,
      },
      {
        code: '3200',
        name: 'الأرباح المحتجزة',
        nameEn: 'Retained Earnings',
        type: AccountType.EQUITY,
        parentId: equity.id,
        level: 2,
      },
    ],
    skipDuplicates: true,
  });

  // Create sub-accounts for Revenue
  await prisma.account.createMany({
    data: [
      {
        code: '4100',
        name: 'إيرادات المبيعات',
        nameEn: 'Sales Revenue',
        type: AccountType.REVENUE,
        parentId: revenue.id,
        level: 2,
      },
      {
        code: '4200',
        name: 'إيرادات أخرى',
        nameEn: 'Other Revenue',
        type: AccountType.REVENUE,
        parentId: revenue.id,
        level: 2,
      },
    ],
    skipDuplicates: true,
  });

  // Create sub-accounts for Expenses
  await prisma.account.createMany({
    data: [
      {
        code: '5100',
        name: 'تكلفة البضاعة المباعة',
        nameEn: 'Cost of Goods Sold',
        type: AccountType.EXPENSE,
        parentId: expenses.id,
        level: 2,
      },
      {
        code: '5200',
        name: 'مصروفات التشغيل',
        nameEn: 'Operating Expenses',
        type: AccountType.EXPENSE,
        parentId: expenses.id,
        level: 2,
      },
      {
        code: '5210',
        name: 'الرواتب والأجور',
        nameEn: 'Salaries and Wages',
        type: AccountType.EXPENSE,
        level: 3,
      },
      {
        code: '5220',
        name: 'الإيجار',
        nameEn: 'Rent',
        type: AccountType.EXPENSE,
        level: 3,
      },
      {
        code: '5230',
        name: 'الكهرباء والماء',
        nameEn: 'Utilities',
        type: AccountType.EXPENSE,
        level: 3,
      },
    ],
    skipDuplicates: true,
  });

  // Create sales-related accounts
  const salesAccounts = await prisma.account.createMany({
    data: [
      {
        code: '4110',
        name: 'المبيعات',
        nameEn: 'Sales',
        type: AccountType.REVENUE,
        parentId: revenue.id,
        level: 2,
      },
      {
        code: '4120',
        name: 'مردودات المبيعات',
        nameEn: 'Sales Returns',
        type: AccountType.REVENUE,
        parentId: revenue.id,
        level: 2,
      },
      {
        code: '1110',
        name: 'النقدية',
        nameEn: 'Cash',
        type: AccountType.ASSET,
        parentId: assets.id,
        level: 2,
      },
      {
        code: '1111',
        name: 'البنك',
        nameEn: 'Bank',
        type: AccountType.ASSET,
        parentId: assets.id,
        level: 2,
      },
    ],
    skipDuplicates: true,
  });

  // Create sample customers
  await prisma.customer.createMany({
    data: [
      {
        code: 'CUST-001',
        name: 'أحمد محمد علي',
        nameEn: 'Ahmed Mohamed Ali',
        phone: '+218-91-1234567',
        email: '<EMAIL>',
        address: 'طرابلس، ليبيا',
        taxNumber: '*********',
      },
      {
        code: 'CUST-002',
        name: 'فاطمة أحمد حسن',
        nameEn: 'Fatima Ahmed Hassan',
        phone: '+218-92-2345678',
        email: '<EMAIL>',
        address: 'بنغازي، ليبيا',
        taxNumber: '*********',
      },
      {
        code: 'CUST-003',
        name: 'محمد عبدالله سالم',
        nameEn: 'Mohamed Abdullah Salem',
        phone: '+218-93-3456789',
        email: '<EMAIL>',
        address: 'مصراتة، ليبيا',
      },
    ],
    skipDuplicates: true,
  });

  // Create sample services
  await prisma.salesService.createMany({
    data: [
      {
        name: 'قص',
        nameEn: 'Cutting',
        price: 50.00,
      },
      {
        name: 'تركيب',
        nameEn: 'Installation',
        price: 100.00,
      },
      {
        name: 'نقل',
        nameEn: 'Transportation',
        price: 75.00,
      },
      {
        name: 'تلميع',
        nameEn: 'Polishing',
        price: 25.00,
      },
      {
        name: 'تغليف',
        nameEn: 'Packaging',
        price: 30.00,
      },
    ],
    skipDuplicates: true,
  });

  // Get required accounts for sales journal settings
  const customersAccount = await prisma.account.findFirst({
    where: { code: '1120' } // العملاء
  });
  
  const salesAccount = await prisma.account.findFirst({
    where: { code: '4110' } // المبيعات
  });
  
  const salesReturnsAccount = await prisma.account.findFirst({
    where: { code: '4120' } // مردودات المبيعات
  });
  
  const cashAccount = await prisma.account.findFirst({
    where: { code: '1110' } // النقدية
  });

  // Create sales journal settings if accounts exist
  if (customersAccount && salesAccount && salesReturnsAccount && cashAccount) {
    await prisma.salesJournalSettings.upsert({
      where: { id: 'default' },
      update: {},
      create: {
        id: 'default',
        salesDebitAccountId: customersAccount.id,      // مدين: العملاء
        salesCreditAccountId: salesAccount.id,         // دائن: المبيعات
        returnDebitAccountId: salesReturnsAccount.id,  // مدين: مردودات المبيعات
        returnCreditAccountId: customersAccount.id,    // دائن: العملاء
        paymentDebitAccountId: cashAccount.id,         // مدين: النقدية
        paymentCreditAccountId: customersAccount.id,   // دائن: العملاء
      },
    });
  }

  // Create default financial period
  await prisma.financialPeriod.upsert({
    where: { id: 'default-2024' },
    update: {},
    create: {
      id: 'default-2024',
      name: 'السنة المالية 2024',
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      isActive: true,
    },
  });

  console.log('✅ Database seeded successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
