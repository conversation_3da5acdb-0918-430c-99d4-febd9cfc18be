(()=>{var e={};e.id=897,e.ids=[897],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18006:(e,s,t)=>{Promise.resolve().then(t.bind(t,77191))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39623:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(60687),r=t(43210),n=t(33213),l=t(78272),c=t(14952),o=t(62688);let i=(0,o.A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),d=(0,o.A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),u=(0,o.A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),p=(0,o.A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);function x(){let e=(0,n.useTranslations)(),[s,t]=(0,r.useState)([]),[o,x]=(0,r.useState)(!0),[m,y]=(0,r.useState)(""),[v,f]=(0,r.useState)(""),[j,b]=(0,r.useState)([]),[g,N]=(0,r.useState)(!1),[E,T]=(0,r.useState)(null),S=async()=>{try{x(!0);let e=await fetch("/api/accounts?includeChildren=true"),s=await e.json();t(w(s))}catch(e){console.error("Error fetching accounts:",e)}finally{x(!1)}},w=e=>{let s=new Map,t=[];return e.forEach(e=>{s.set(e.id,{...e,children:[]})}),e.forEach(e=>{let a=s.get(e.id);if(e.parentId){let t=s.get(e.parentId);t&&t.children.push(a)}else t.push(a)}),t.sort((e,s)=>e.code.localeCompare(s.code))},A=e=>{b(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},C=s.filter(e=>{let s=e.name.toLowerCase().includes(m.toLowerCase())||e.code.includes(m),t=!v||e.type===v;return s&&t}),k=e=>({ASSET:"bg-blue-100 text-blue-800",LIABILITY:"bg-red-100 text-red-800",EQUITY:"bg-purple-100 text-purple-800",REVENUE:"bg-green-100 text-green-800",EXPENSE:"bg-orange-100 text-orange-800"})[e]||"bg-gray-100 text-gray-800",P=(s,t=0)=>{let r=j.includes(s.id),n=s.children&&s.children.length>0;return(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center py-3 px-4 hover:bg-gray-50 border-b border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center flex-1",style:{paddingLeft:`${24*t}px`},children:[n?(0,a.jsx)("button",{onClick:()=>A(s.id),className:"mr-2 p-1 hover:bg-gray-200 rounded",children:r?(0,a.jsx)(l.A,{className:"w-4 h-4"}):(0,a.jsx)(c.A,{className:"w-4 h-4"})}):(0,a.jsx)("div",{className:"w-6 mr-2"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("span",{className:"font-mono text-sm text-gray-600",children:s.code}),(0,a.jsx)("span",{className:"font-medium text-gray-900",children:s.name}),(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${k(s.type)}`,children:e(`accountTypes.${s.type}`)})]})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[s.balance?.toLocaleString()||"0"," د.ل"]}),(0,a.jsx)("button",{onClick:()=>T(s),className:"p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded",children:(0,a.jsx)(i,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>I(s),className:"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded",disabled:s._count&&(s._count.children>0||s._count.debitEntries>0||s._count.creditEntries>0),children:(0,a.jsx)(d,{className:"w-4 h-4"})})]})]}),r&&n&&(0,a.jsx)("div",{children:s.children.map(e=>P(e,t+1))})]},s.id)},I=async s=>{if(s._count&&(s._count.children>0||s._count.debitEntries>0||s._count.creditEntries>0))return void alert(e("errors.cannotDeleteAccount"));if(confirm(`هل أنت متأكد من حذف الحساب "${s.name}"؟`))try{let e=await fetch(`/api/accounts/${s.id}`,{method:"DELETE"});if(e.ok)S();else{let s=await e.json();alert(s.error||"حدث خطأ أثناء حذف الحساب")}}catch(e){console.error("Error deleting account:",e),alert("حدث خطأ أثناء حذف الحساب")}};return o?(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)("div",{className:"spinner"}),(0,a.jsx)("span",{className:"ml-2",children:e("common.loading")})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e("accounts.chartOfAccounts")}),(0,a.jsxs)("button",{onClick:()=>N(!0),className:"btn-primary flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)(u,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e("accounts.addAccount")})]})]}),(0,a.jsx)("div",{className:"card",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p,{className:"absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:e("common.search"),value:m,onChange:e=>y(e.target.value),className:"input-field pl-10 rtl:pl-3 rtl:pr-10"})]})}),(0,a.jsx)("div",{className:"sm:w-48",children:(0,a.jsxs)("select",{value:v,onChange:e=>f(e.target.value),className:"input-field",children:[(0,a.jsxs)("option",{value:"",children:[e("common.type")," - الكل"]}),(0,a.jsx)("option",{value:"ASSET",children:e("accountTypes.ASSET")}),(0,a.jsx)("option",{value:"LIABILITY",children:e("accountTypes.LIABILITY")}),(0,a.jsx)("option",{value:"EQUITY",children:e("accountTypes.EQUITY")}),(0,a.jsx)("option",{value:"REVENUE",children:e("accountTypes.REVENUE")}),(0,a.jsx)("option",{value:"EXPENSE",children:e("accountTypes.EXPENSE")})]})})]})}),(0,a.jsxs)("div",{className:"card p-0",children:[(0,a.jsx)("div",{className:"bg-gray-50 px-4 py-3 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"شجرة الحسابات"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[C.length," حساب"]})]})}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===C.length?(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"لا توجد حسابات مطابقة للبحث"}):C.map(e=>P(e))})]}),(g||E)&&(0,a.jsx)(h,{account:E,onClose:()=>{N(!1),T(null)},onSave:()=>{S(),N(!1),T(null)}})]})}function h({account:e,onClose:s,onSave:t}){let l=(0,n.useTranslations)(),[c,o]=(0,r.useState)({code:e?.code||"",name:e?.name||"",nameEn:e?.nameEn||"",type:e?.type||"ASSET",parentId:e?.parentId||""}),i=async s=>{s.preventDefault();try{let s=e?`/api/accounts/${e.id}`:"/api/accounts",a=e?"PUT":"POST",r=await fetch(s,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(c)});if(r.ok)t();else{let e=await r.json();alert(e.error||"حدث خطأ")}}catch(e){console.error("Error saving account:",e),alert("حدث خطأ أثناء حفظ الحساب")}};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:l(e?"accounts.editAccount":"accounts.addAccount")}),(0,a.jsxs)("form",{onSubmit:i,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:l("accounts.accountCode")}),(0,a.jsx)("input",{type:"text",value:c.code,onChange:e=>o({...c,code:e.target.value}),className:"input-field",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:l("accounts.accountName")}),(0,a.jsx)("input",{type:"text",value:c.name,onChange:e=>o({...c,name:e.target.value}),className:"input-field",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:l("accounts.accountType")}),(0,a.jsxs)("select",{value:c.type,onChange:e=>o({...c,type:e.target.value}),className:"input-field",children:[(0,a.jsx)("option",{value:"ASSET",children:l("accountTypes.ASSET")}),(0,a.jsx)("option",{value:"LIABILITY",children:l("accountTypes.LIABILITY")}),(0,a.jsx)("option",{value:"EQUITY",children:l("accountTypes.EQUITY")}),(0,a.jsx)("option",{value:"REVENUE",children:l("accountTypes.REVENUE")}),(0,a.jsx)("option",{value:"EXPENSE",children:l("accountTypes.EXPENSE")})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3 rtl:space-x-reverse pt-4",children:[(0,a.jsx)("button",{type:"submit",className:"btn-primary flex-1",children:l("common.save")}),(0,a.jsx)("button",{type:"button",onClick:s,className:"btn-secondary flex-1",children:l("common.cancel")})]})]})]})})}},41566:(e,s,t)=>{Promise.resolve().then(t.bind(t,39623))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70521:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>i});var a=t(65239),r=t(48088),n=t(88170),l=t.n(n),c=t(30893),o={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);t.d(s,o);let i={children:["",{children:["[locale]",{children:["(dashboard)",{children:["accounts",{children:["chart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,77191)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\(dashboard)\\accounts\\chart\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,55338)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\(dashboard)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,11434)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,66196)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\error.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\(dashboard)\\accounts\\chart\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[locale]/(dashboard)/accounts/chart/page",pathname:"/[locale]/accounts/chart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},77191:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\accounts\\\\chart\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\(dashboard)\\accounts\\chart\\page.tsx","default")}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,756,207,87],()=>t(70521));module.exports=a})();