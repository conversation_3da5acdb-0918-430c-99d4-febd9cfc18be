{"/_not-found/page": "app/_not-found/page.js", "/api/accounts/[id]/route": "app/api/accounts/[id]/route.js", "/api/settings/route": "app/api/settings/route.js", "/api/journals/route": "app/api/journals/route.js", "/api/accounts/route": "app/api/accounts/route.js", "/api/reports/trial-balance/route": "app/api/reports/trial-balance/route.js", "/[locale]/(dashboard)/page": "app/[locale]/(dashboard)/page.js", "/[locale]/(dashboard)/payroll/page": "app/[locale]/(dashboard)/payroll/page.js", "/[locale]/(dashboard)/sales/page": "app/[locale]/(dashboard)/sales/page.js", "/[locale]/(dashboard)/inventory/page": "app/[locale]/(dashboard)/inventory/page.js", "/[locale]/(dashboard)/settings/page": "app/[locale]/(dashboard)/settings/page.js", "/[locale]/(dashboard)/payments/page": "app/[locale]/(dashboard)/payments/page.js", "/[locale]/(dashboard)/purchases/page": "app/[locale]/(dashboard)/purchases/page.js", "/[locale]/(dashboard)/accounts/chart/page": "app/[locale]/(dashboard)/accounts/chart/page.js", "/[locale]/(dashboard)/receipts/page": "app/[locale]/(dashboard)/receipts/page.js"}