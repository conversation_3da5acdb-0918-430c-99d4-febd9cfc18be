(()=>{var e={};e.id=794,e.ids=[794],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32087:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>p});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),c=r(94747);async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("type"),s=t.get("parentId"),n="true"===t.get("includeChildren"),a={};r&&(a.type=r),s?a.parentId=s:null===s&&(a.parentId=null);let o=await c.z.account.findMany({where:a,include:{parent:!0,children:n,_count:{select:{children:!0,debitEntries:!0,creditEntries:!0}}},orderBy:{code:"asc"}});return i.NextResponse.json(o)}catch(e){return console.error("Error fetching accounts:",e),i.NextResponse.json({error:"Failed to fetch accounts"},{status:500})}}async function p(e){try{let{code:t,name:r,nameEn:s,type:n,parentId:a,level:o}=await e.json();if(!t||!r||!n)return i.NextResponse.json({error:"Missing required fields"},{status:400});if(await c.z.account.findUnique({where:{code:t}}))return i.NextResponse.json({error:"Account code already exists"},{status:400});let u=await c.z.account.create({data:{code:t,name:r,nameEn:s,type:n,parentId:a,level:o||1},include:{parent:!0,children:!0}});return i.NextResponse.json(u,{status:201})}catch(e){return console.error("Error creating account:",e),i.NextResponse.json({error:"Failed to create account"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/accounts/route",pathname:"/api/accounts",filename:"route",bundlePath:"app/api/accounts/route"},resolvedPagePath:"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\api\\accounts\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:h}=d;function g(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},94747:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});let s=require("@prisma/client"),n=globalThis.prisma??new s.PrismaClient},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(32087));module.exports=s})();