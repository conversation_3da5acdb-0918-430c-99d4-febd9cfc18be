(()=>{var e={};e.id=409,e.ids=[409],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},81935:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>b,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var a={};r.r(a),r.d(a,{GET:()=>c});var i=r(96559),n=r(48088),s=r(37719),o=r(32190),l=r(94747);async function c(e){try{let{searchParams:t}=new URL(e.url),r=t.get("startDate"),a=t.get("endDate"),i={};(r||a)&&(i={},r&&(i.gte=new Date(r)),a&&(i.lte=new Date(a)));let n=(await l.z.account.findMany({where:{isActive:!0},include:{debitEntries:{where:{journal:{isPosted:!0,...Object.keys(i).length>0&&{date:i}}}},creditEntries:{where:{journal:{isPosted:!0,...Object.keys(i).length>0&&{date:i}}}}},orderBy:{code:"asc"}})).map(e=>{let t=e.debitEntries.reduce((e,t)=>e+Number(t.debit),0),r=e.creditEntries.reduce((e,t)=>e+Number(t.credit),0),a=0,i=0,n=t-r;return["ASSET","EXPENSE"].includes(e.type)?n>=0?a=n:i=Math.abs(n):n<=0?i=Math.abs(n):a=n,{id:e.id,code:e.code,name:e.name,nameEn:e.nameEn,type:e.type,level:e.level,totalDebit:t,totalCredit:r,debitBalance:a,creditBalance:i}}).filter(e=>e.debitBalance>0||e.creditBalance>0),s=n.reduce((e,t)=>({totalDebit:e.totalDebit+t.totalDebit,totalCredit:e.totalCredit+t.totalCredit,totalDebitBalance:e.totalDebitBalance+t.debitBalance,totalCreditBalance:e.totalCreditBalance+t.creditBalance}),{totalDebit:0,totalCredit:0,totalDebitBalance:0,totalCreditBalance:0});return o.NextResponse.json({accounts:n,totals:s,period:{startDate:r,endDate:a}})}catch(e){return console.error("Error generating trial balance:",e),o.NextResponse.json({error:"Failed to generate trial balance"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/reports/trial-balance/route",pathname:"/api/reports/trial-balance",filename:"route",bundlePath:"app/api/reports/trial-balance/route"},resolvedPagePath:"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\api\\reports\\trial-balance\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:b}=d;function m(){return(0,s.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}},94747:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});let a=require("@prisma/client"),i=globalThis.prisma??new a.PrismaClient},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,580],()=>r(81935));module.exports=a})();