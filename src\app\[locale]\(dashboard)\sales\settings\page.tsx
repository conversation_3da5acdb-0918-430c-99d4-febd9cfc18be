'use client';

import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { 
  Save, 
  Settings, 
  FileText,
  DollarSign,
  ArrowRight,
  ArrowLeft
} from 'lucide-react';

interface Account {
  id: string;
  code: string;
  name: string;
  type: string;
}

interface JournalSettings {
  salesDebitAccountId: string;
  salesCreditAccountId: string;
  returnDebitAccountId: string;
  returnCreditAccountId: string;
  paymentDebitAccountId: string;
  paymentCreditAccountId: string;
}

export default function SalesSettingsPage() {
  const t = useTranslations('sales');
  const params = useParams();
  const locale = params.locale as string;
  
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [settings, setSettings] = useState<JournalSettings>({
    salesDebitAccountId: '',
    salesCreditAccountId: '',
    returnDebitAccountId: '',
    returnCreditAccountId: '',
    paymentDebitAccountId: '',
    paymentCreditAccountId: ''
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Mock data loading
  useEffect(() => {
    const mockAccounts: Account[] = [
      { id: '1', code: '1120', name: 'العملاء', type: 'ASSET' },
      { id: '2', code: '4110', name: 'المبيعات', type: 'REVENUE' },
      { id: '3', code: '4120', name: 'مردودات المبيعات', type: 'REVENUE' },
      { id: '4', code: '1110', name: 'النقدية', type: 'ASSET' },
      { id: '5', code: '1111', name: 'البنك', type: 'ASSET' }
    ];
    
    const mockSettings: JournalSettings = {
      salesDebitAccountId: '1', // العملاء
      salesCreditAccountId: '2', // المبيعات
      returnDebitAccountId: '3', // مردودات المبيعات
      returnCreditAccountId: '1', // العملاء
      paymentDebitAccountId: '4', // النقدية
      paymentCreditAccountId: '1' // العملاء
    };
    
    setTimeout(() => {
      setAccounts(mockAccounts);
      setSettings(mockSettings);
      setLoading(false);
    }, 1000);
  }, []);

  const handleSave = async () => {
    setSaving(true);
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('تم حفظ الإعدادات بنجاح');
    } catch (error) {
      alert('حدث خطأ أثناء حفظ الإعدادات');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t('settings')}</h1>
          <p className="text-gray-600">إعدادات موديول المبيعات والقيود المحاسبية</p>
        </div>
        <button
          onClick={handleSave}
          disabled={saving}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
        >
          <Save className="w-4 h-4 mr-2" />
          {saving ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Journal Settings */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              {t('journalSettings')}
            </h2>
            
            <div className="space-y-6">
              {/* Sales Entry */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                  <FileText className="w-4 h-4 mr-2" />
                  {t('salesEntry')}
                </h3>
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('debitAccount')} (العملاء)
                    </label>
                    <select
                      value={settings.salesDebitAccountId}
                      onChange={(e) => setSettings({...settings, salesDebitAccountId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">اختر الحساب</option>
                      {accounts.filter(acc => acc.type === 'ASSET').map(account => (
                        <option key={account.id} value={account.id}>
                          {account.code} - {account.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="flex justify-center">
                    <ArrowRight className="w-4 h-4 text-gray-400" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('creditAccount')} (المبيعات)
                    </label>
                    <select
                      value={settings.salesCreditAccountId}
                      onChange={(e) => setSettings({...settings, salesCreditAccountId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">اختر الحساب</option>
                      {accounts.filter(acc => acc.type === 'REVENUE').map(account => (
                        <option key={account.id} value={account.id}>
                          {account.code} - {account.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

              {/* Return Entry */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  {t('returnEntry')}
                </h3>
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('debitAccount')} (مردودات المبيعات)
                    </label>
                    <select
                      value={settings.returnDebitAccountId}
                      onChange={(e) => setSettings({...settings, returnDebitAccountId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">اختر الحساب</option>
                      {accounts.filter(acc => acc.type === 'REVENUE').map(account => (
                        <option key={account.id} value={account.id}>
                          {account.code} - {account.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="flex justify-center">
                    <ArrowRight className="w-4 h-4 text-gray-400" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('creditAccount')} (العملاء)
                    </label>
                    <select
                      value={settings.returnCreditAccountId}
                      onChange={(e) => setSettings({...settings, returnCreditAccountId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">اختر الحساب</option>
                      {accounts.filter(acc => acc.type === 'ASSET').map(account => (
                        <option key={account.id} value={account.id}>
                          {account.code} - {account.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

              {/* Payment Entry */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                  <DollarSign className="w-4 h-4 mr-2" />
                  {t('paymentEntry')}
                </h3>
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('debitAccount')} (النقدية/البنك)
                    </label>
                    <select
                      value={settings.paymentDebitAccountId}
                      onChange={(e) => setSettings({...settings, paymentDebitAccountId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">اختر الحساب</option>
                      {accounts.filter(acc => acc.type === 'ASSET').map(account => (
                        <option key={account.id} value={account.id}>
                          {account.code} - {account.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="flex justify-center">
                    <ArrowRight className="w-4 h-4 text-gray-400" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('creditAccount')} (العملاء)
                    </label>
                    <select
                      value={settings.paymentCreditAccountId}
                      onChange={(e) => setSettings({...settings, paymentCreditAccountId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">اختر الحساب</option>
                      {accounts.filter(acc => acc.type === 'ASSET').map(account => (
                        <option key={account.id} value={account.id}>
                          {account.code} - {account.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* General Settings */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">الإعدادات العامة</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  تلقائي الترحيل
                </label>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    defaultChecked={true}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="mr-2 block text-sm text-gray-900">
                    ترحيل القيود تلقائياً عند حفظ الفاتورة النهائية
                  </label>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الضريبة الافتراضية (%)
                </label>
                <input
                  type="number"
                  defaultValue={0}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الخصم الافتراضي (%)
                </label>
                <input
                  type="number"
                  defaultValue={0}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  بادئة رقم الفاتورة
                </label>
                <input
                  type="text"
                  defaultValue="S-"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  عدد الأرقام التسلسلية
                </label>
                <input
                  type="number"
                  defaultValue={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Help Section */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-medium text-blue-900 mb-3">مساعدة</h3>
            <div className="space-y-2 text-sm text-blue-800">
              <p>• قيد البيع: يتم ترحيله تلقائياً عند حفظ الفاتورة النهائية</p>
              <p>• قيد المرتجع: يتم ترحيله عند إلغاء الفاتورة</p>
              <p>• قيد السداد: يتم ترحيله عند إضافة سداد للفاتورة</p>
              <p>• تأكد من اختيار الحسابات الصحيحة لكل نوع قيد</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 