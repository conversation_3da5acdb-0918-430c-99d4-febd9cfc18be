(()=>{var e={};e.id=258,e.ids=[258],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},20185:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>j});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>d});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(94747);async function c(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),s=parseInt(t.get("limit")||"10"),n=t.get("type"),a=t.get("isPosted"),o=t.get("startDate"),c=t.get("endDate"),d=(r-1)*s,l={};n&&(l.type=n),null!==a&&(l.isPosted="true"===a),(o||c)&&(l.date={},o&&(l.date.gte=new Date(o)),c&&(l.date.lte=new Date(c)));let[p,j]=await Promise.all([u.z.journal.findMany({where:l,include:{entries:{include:{account:!0,creditAccount:!0}}},orderBy:{date:"desc"},skip:d,take:s}),u.z.journal.count({where:l})]);return i.NextResponse.json({journals:p,pagination:{page:r,limit:s,total:j,pages:Math.ceil(j/s)}})}catch(e){return console.error("Error fetching journals:",e),i.NextResponse.json({error:"Failed to fetch journals"},{status:500})}}async function d(e){try{let{number:t,date:r,description:s,reference:n,type:a,entries:o}=await e.json();if(!t||!r||!s||!o||0===o.length)return i.NextResponse.json({error:"Missing required fields"},{status:400});let c=o.reduce((e,t)=>e+(t.debit||0),0),d=o.reduce((e,t)=>e+(t.credit||0),0);if(Math.abs(c-d)>.01)return i.NextResponse.json({error:"Debits must equal credits"},{status:400});if(await u.z.journal.findUnique({where:{number:t}}))return i.NextResponse.json({error:"Journal number already exists"},{status:400});let l=await u.z.journal.create({data:{number:t,date:new Date(r),description:s,reference:n,type:a||"MANUAL",totalDebit:c,totalCredit:d,entries:{create:o.map(e=>({accountId:e.accountId,creditAccountId:e.creditAccountId,description:e.description,debit:e.debit||0,credit:e.credit||0}))}},include:{entries:{include:{account:!0,creditAccount:!0}}}});return i.NextResponse.json(l,{status:201})}catch(e){return console.error("Error creating journal:",e),i.NextResponse.json({error:"Failed to create journal"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/journals/route",pathname:"/api/journals",filename:"route",bundlePath:"app/api/journals/route"},resolvedPagePath:"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\api\\journals\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:j,serverHooks:x}=l;function g(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:j})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},94747:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});let s=require("@prisma/client"),n=globalThis.prisma??new s.PrismaClient},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(20185));module.exports=s})();