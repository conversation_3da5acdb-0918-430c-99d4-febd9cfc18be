(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[897],{13052:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},18970:(e,t,s)=>{Promise.resolve().then(s.bind(s,60404))},60404:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var a=s(95155),c=s(12115),n=s(27043),l=s(66474),r=s(13052),i=s(19946);let o=(0,i.A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),d=(0,i.A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),u=(0,i.A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),x=(0,i.A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);function h(){let e=(0,n.useTranslations)(),[t,s]=(0,c.useState)([]),[i,h]=(0,c.useState)(!0),[p,y]=(0,c.useState)(""),[v,j]=(0,c.useState)(""),[f,g]=(0,c.useState)([]),[N,b]=(0,c.useState)(!1),[E,T]=(0,c.useState)(null);(0,c.useEffect)(()=>{S()},[]);let S=async()=>{try{h(!0);let e=await fetch("/api/accounts?includeChildren=true"),t=await e.json();s(k(t))}catch(e){console.error("Error fetching accounts:",e)}finally{h(!1)}},k=e=>{let t=new Map,s=[];return e.forEach(e=>{t.set(e.id,{...e,children:[]})}),e.forEach(e=>{let a=t.get(e.id);if(e.parentId){let s=t.get(e.parentId);s&&s.children.push(a)}else s.push(a)}),s.sort((e,t)=>e.code.localeCompare(t.code))},w=e=>{g(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},A=t.filter(e=>{let t=e.name.toLowerCase().includes(p.toLowerCase())||e.code.includes(p),s=!v||e.type===v;return t&&s}),I=e=>({ASSET:"bg-blue-100 text-blue-800",LIABILITY:"bg-red-100 text-red-800",EQUITY:"bg-purple-100 text-purple-800",REVENUE:"bg-green-100 text-green-800",EXPENSE:"bg-orange-100 text-orange-800"})[e]||"bg-gray-100 text-gray-800",C=function(t){var s;let c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=f.includes(t.id),i=t.children&&t.children.length>0;return(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center py-3 px-4 hover:bg-gray-50 border-b border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center flex-1",style:{paddingLeft:"".concat(24*c,"px")},children:[i?(0,a.jsx)("button",{onClick:()=>w(t.id),className:"mr-2 p-1 hover:bg-gray-200 rounded",children:n?(0,a.jsx)(l.A,{className:"w-4 h-4"}):(0,a.jsx)(r.A,{className:"w-4 h-4"})}):(0,a.jsx)("div",{className:"w-6 mr-2"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,a.jsx)("span",{className:"font-mono text-sm text-gray-600",children:t.code}),(0,a.jsx)("span",{className:"font-medium text-gray-900",children:t.name}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(I(t.type)),children:e("accountTypes.".concat(t.type))})]})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[(null==(s=t.balance)?void 0:s.toLocaleString())||"0"," د.ل"]}),(0,a.jsx)("button",{onClick:()=>T(t),className:"p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded",children:(0,a.jsx)(o,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>L(t),className:"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded",disabled:t._count&&(t._count.children>0||t._count.debitEntries>0||t._count.creditEntries>0),children:(0,a.jsx)(d,{className:"w-4 h-4"})})]})]}),n&&i&&(0,a.jsx)("div",{children:t.children.map(e=>C(e,c+1))})]},t.id)},L=async t=>{if(t._count&&(t._count.children>0||t._count.debitEntries>0||t._count.creditEntries>0))return void alert(e("errors.cannotDeleteAccount"));if(confirm('هل أنت متأكد من حذف الحساب "'.concat(t.name,'"؟')))try{let e=await fetch("/api/accounts/".concat(t.id),{method:"DELETE"});if(e.ok)S();else{let t=await e.json();alert(t.error||"حدث خطأ أثناء حذف الحساب")}}catch(e){console.error("Error deleting account:",e),alert("حدث خطأ أثناء حذف الحساب")}};return i?(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)("div",{className:"spinner"}),(0,a.jsx)("span",{className:"ml-2",children:e("common.loading")})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e("accounts.chartOfAccounts")}),(0,a.jsxs)("button",{onClick:()=>b(!0),className:"btn-primary flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)(u,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e("accounts.addAccount")})]})]}),(0,a.jsx)("div",{className:"card",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(x,{className:"absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:e("common.search"),value:p,onChange:e=>y(e.target.value),className:"input-field pl-10 rtl:pl-3 rtl:pr-10"})]})}),(0,a.jsx)("div",{className:"sm:w-48",children:(0,a.jsxs)("select",{value:v,onChange:e=>j(e.target.value),className:"input-field",children:[(0,a.jsxs)("option",{value:"",children:[e("common.type")," - الكل"]}),(0,a.jsx)("option",{value:"ASSET",children:e("accountTypes.ASSET")}),(0,a.jsx)("option",{value:"LIABILITY",children:e("accountTypes.LIABILITY")}),(0,a.jsx)("option",{value:"EQUITY",children:e("accountTypes.EQUITY")}),(0,a.jsx)("option",{value:"REVENUE",children:e("accountTypes.REVENUE")}),(0,a.jsx)("option",{value:"EXPENSE",children:e("accountTypes.EXPENSE")})]})})]})}),(0,a.jsxs)("div",{className:"card p-0",children:[(0,a.jsx)("div",{className:"bg-gray-50 px-4 py-3 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"شجرة الحسابات"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[A.length," حساب"]})]})}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===A.length?(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"لا توجد حسابات مطابقة للبحث"}):A.map(e=>C(e))})]}),(N||E)&&(0,a.jsx)(m,{account:E,onClose:()=>{b(!1),T(null)},onSave:()=>{S(),b(!1),T(null)}})]})}function m(e){let{account:t,onClose:s,onSave:l}=e,r=(0,n.useTranslations)(),[i,o]=(0,c.useState)({code:(null==t?void 0:t.code)||"",name:(null==t?void 0:t.name)||"",nameEn:(null==t?void 0:t.nameEn)||"",type:(null==t?void 0:t.type)||"ASSET",parentId:(null==t?void 0:t.parentId)||""}),d=async e=>{e.preventDefault();try{let e=t?"/api/accounts/".concat(t.id):"/api/accounts",s=t?"PUT":"POST",a=await fetch(e,{method:s,headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(a.ok)l();else{let e=await a.json();alert(e.error||"حدث خطأ")}}catch(e){console.error("Error saving account:",e),alert("حدث خطأ أثناء حفظ الحساب")}};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:r(t?"accounts.editAccount":"accounts.addAccount")}),(0,a.jsxs)("form",{onSubmit:d,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:r("accounts.accountCode")}),(0,a.jsx)("input",{type:"text",value:i.code,onChange:e=>o({...i,code:e.target.value}),className:"input-field",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:r("accounts.accountName")}),(0,a.jsx)("input",{type:"text",value:i.name,onChange:e=>o({...i,name:e.target.value}),className:"input-field",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:r("accounts.accountType")}),(0,a.jsxs)("select",{value:i.type,onChange:e=>o({...i,type:e.target.value}),className:"input-field",children:[(0,a.jsx)("option",{value:"ASSET",children:r("accountTypes.ASSET")}),(0,a.jsx)("option",{value:"LIABILITY",children:r("accountTypes.LIABILITY")}),(0,a.jsx)("option",{value:"EQUITY",children:r("accountTypes.EQUITY")}),(0,a.jsx)("option",{value:"REVENUE",children:r("accountTypes.REVENUE")}),(0,a.jsx)("option",{value:"EXPENSE",children:r("accountTypes.EXPENSE")})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3 rtl:space-x-reverse pt-4",children:[(0,a.jsx)("button",{type:"submit",className:"btn-primary flex-1",children:r("common.save")}),(0,a.jsx)("button",{type:"button",onClick:s,className:"btn-secondary flex-1",children:r("common.cancel")})]})]})]})})}},66474:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[320,441,684,358],()=>t(18970)),_N_E=e.O()}]);