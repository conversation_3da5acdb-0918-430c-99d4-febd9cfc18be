{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(ar|en))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/(ar|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Hzzn7Q5_I531Drz07rH6x", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "odjT2/htp4ffsuHVPq8Yfqe4EWaRzsLAoBtJhs0qt/c=", "__NEXT_PREVIEW_MODE_ID": "85efd49fd52d73502353831a630bb9a7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "04ed103b08308923ffd4cdf339a0280d561224f61b34571e0c6ffb7daf4b15b0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "77f4cc1d643f924ab018783d1913978200fdd7a401d6ce77357db45218eca696"}}}, "functions": {}, "sortedMiddleware": ["/"]}