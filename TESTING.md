# دليل الاختبار - Testing Guide

## 🚀 رابط الاختبار المباشر

**الرابط الحالي**: http://localhost:3003

هذا الرابط محدث دائماً ويعكس آخر التطورات في النظام.

## 📋 حالة النظام الحالية

### ✅ ما يعمل بشكل كامل
- **الصفحة الرئيسية**: لوحة التحكم مع الإحصائيات
- **وحدة الحسابات**: 
  - شجرة الحسابات
  - إضافة/تعديل/حذف الحسابات
  - القيود اليومية
  - التقارير المالية
- **إعدادات الشركة**: رفع الشعار والبيانات الأساسية
- **تبديل اللغة**: العربية والإنجليزية
- **تبديل العملة**: دينار ليبي، جنيه مصري، ريال سعودي

### 🚧 الوحدات تحت التطوير
- المبيعات
- المشتريات  
- المخزون
- الرواتب
- المدفوعات
- المقبوضات

## 🧪 سيناريوهات الاختبار

### 1. اختبار الصفحة الرئيسية
1. افتح http://localhost:3003
2. تحقق من عرض لوحة التحكم
3. تحقق من الإحصائيات والرسوم البيانية
4. اختبر تبديل اللغة (أعلى يمين الصفحة)

### 2. اختبار وحدة الحسابات
1. انقر على "الحسابات" في القائمة الجانبية
2. اختبر عرض شجرة الحسابات
3. جرب إضافة حساب جديد
4. اختبر تعديل حساب موجود
5. جرب حذف حساب (إذا لم يكن له أرصدة)

### 3. اختبار القيود اليومية
1. اذهب إلى "الحسابات" > "القيود اليومية"
2. جرب إضافة قيد جديد
3. تأكد من توازن القيد (مدين = دائن)
4. اختبر حفظ القيد

### 4. اختبار التقارير
1. اذهب إلى "الحسابات" > "التقارير"
2. اختبر ميزان المراجعة
3. جرب قائمة الدخل
4. اختبر قائمة المركز المالي

### 5. اختبار إعدادات الشركة
1. انقر على "الإعدادات" في القائمة الجانبية
2. جرب رفع شعار جديد
3. اختبر تعديل بيانات الشركة
4. احفظ التغييرات وتحقق من ظهورها

## 🔧 أدوات المراقبة

### فحص صحة التطبيق
```bash
# فحص يدوي
npm run health

# مراقبة مستمرة
node scripts/health-check.js
```

### مراقبة الأخطاء
- تحقق من console المتصفح للأخطاء
- راجع terminal التطوير للأخطاء الخلفية
- ملفات السجلات في مجلد `logs/`

## 🐛 الأخطاء المعروفة والحلول

### مشكلة Next.js 15 Parameters
**المشكلة**: أخطاء متعلقة بـ `params` في Next.js 15
**الحل**: تم إصلاحها - تحديث الكود لاستخدام `await params`

### مشكلة قاعدة البيانات
**المشكلة**: خطأ في الاتصال بقاعدة البيانات
**الحل**: 
```bash
npm run db:generate
npm run db:push
```

### مشكلة المنافذ
**المشكلة**: المنفذ 3000 مستخدم
**الحل**: التطبيق يعمل تلقائياً على المنفذ 3003

## 📊 مؤشرات الأداء

### أوقات الاستجابة المتوقعة
- الصفحة الرئيسية: < 2 ثانية
- تحميل الحسابات: < 1 ثانية  
- حفظ البيانات: < 3 ثواني
- التقارير: < 5 ثواني

### استخدام الذاكرة
- الحد الأدنى: 100 MB
- الحد الطبيعي: 200-300 MB
- الحد الأقصى: 500 MB

## 🔄 التحديثات التلقائية

النظام يدعم Hot Reload، مما يعني:
- التغييرات في الكود تظهر فوراً
- لا حاجة لإعادة تحميل الصفحة
- قاعدة البيانات تبقى كما هي

## 📞 الإبلاغ عن المشاكل

عند العثور على مشكلة، يرجى تسجيل:
1. **الخطوات**: كيفية الوصول للمشكلة
2. **النتيجة المتوقعة**: ما كان يجب أن يحدث
3. **النتيجة الفعلية**: ما حدث بالفعل
4. **المتصفح**: نوع وإصدار المتصفح
5. **لقطة شاشة**: إن أمكن

## 🎯 أهداف الاختبار

- ✅ التأكد من عمل جميع الوظائف الأساسية
- ✅ اختبار الاستجابة على أجهزة مختلفة
- ✅ التحقق من دعم اللغات والعملات
- ✅ اختبار الأداء والسرعة
- ✅ التأكد من أمان البيانات

---

**آخر تحديث**: تم إصلاح مشاكل Next.js 15 وتحسين الأداء
**حالة النظام**: 🟢 يعمل بشكل طبيعي
