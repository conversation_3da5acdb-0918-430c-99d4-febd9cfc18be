'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Printer, 
  Trash2,
  DollarSign,
  Calendar,
  User,
  FileText
} from 'lucide-react';

interface Invoice {
  id: string;
  number: string;
  date: string;
  customer: {
    name: string;
  };
  status: 'DRAFT' | 'FINAL' | 'CANCELLED';
  total: number;
  paid: number;
  remaining: number;
  isPosted: boolean;
}

export default function InvoicesPage() {
  const t = useTranslations('sales');
  const params = useParams();
  const locale = params.locale as string;
  
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');

  // Mock data - سيتم استبدالها بـ API calls
  useEffect(() => {
    const mockInvoices: Invoice[] = [
      {
        id: '1',
        number: 'S-001',
        date: '2024-01-15',
        customer: { name: 'أحمد محمد علي' },
        status: 'FINAL',
        total: 1500.00,
        paid: 1500.00,
        remaining: 0,
        isPosted: true
      },
      {
        id: '2',
        number: 'S-002',
        date: '2024-01-16',
        customer: { name: 'فاطمة أحمد حسن' },
        status: 'DRAFT',
        total: 2300.00,
        paid: 0,
        remaining: 2300.00,
        isPosted: false
      },
      {
        id: '3',
        number: 'S-003',
        date: '2024-01-17',
        customer: { name: 'محمد عبدالله سالم' },
        status: 'FINAL',
        total: 800.00,
        paid: 500.00,
        remaining: 300.00,
        isPosted: true
      }
    ];
    
    setTimeout(() => {
      setInvoices(mockInvoices);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">مبدئية</span>;
      case 'FINAL':
        return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">نهائية</span>;
      case 'CANCELLED':
        return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">ملغاة</span>;
      default:
        return <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">غير محدد</span>;
    }
  };

  const getPaymentStatus = (paid: number, total: number) => {
    if (paid === 0) return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">غير مسدد</span>;
    if (paid === total) return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">مسدد</span>;
    return <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">مسدد جزئياً</span>;
  };

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.customer.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'ALL' || invoice.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t('invoices')}</h1>
          <p className="text-gray-600">إدارة فواتير المبيعات</p>
        </div>
        <Link
          href={`/${locale}/sales/invoices/create`}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
        >
          <Plus className="w-4 h-4 mr-2" />
          {t('createInvoice')}
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="البحث في الفواتير..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="ALL">جميع الحالات</option>
            <option value="DRAFT">مبدئية</option>
            <option value="FINAL">نهائية</option>
            <option value="CANCELLED">ملغاة</option>
          </select>
          
          <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center">
            <Filter className="w-4 h-4 mr-2" />
            فلترة متقدمة
          </button>
        </div>
      </div>

      {/* Invoices Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('invoiceNumber')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('invoiceDate')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('customer')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('status')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('total')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('paymentStatus')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الترحيل
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredInvoices.map((invoice) => (
                <tr key={invoice.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <FileText className="w-4 h-4 text-blue-500 mr-2" />
                      <span className="text-sm font-medium text-gray-900">{invoice.number}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(invoice.date).toLocaleDateString('ar-LY')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <User className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900">{invoice.customer.name}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(invoice.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {invoice.total.toLocaleString('ar-LY')} د.ل
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getPaymentStatus(invoice.paid, invoice.total)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {invoice.isPosted ? (
                      <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">مرحل</span>
                    ) : (
                      <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">غير مرحل</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button className="text-blue-600 hover:text-blue-900">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-green-600 hover:text-green-900">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="text-purple-600 hover:text-purple-900">
                        <Printer className="w-4 h-4" />
                      </button>
                      <button className="text-red-600 hover:text-red-900">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {filteredInvoices.length === 0 && (
        <div className="text-center py-12">
          <FileText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد فواتير</h3>
          <p className="mt-1 text-sm text-gray-500">ابدأ بإنشاء فاتورة جديدة</p>
          <div className="mt-6">
            <Link
              href={`/${locale}/sales/invoices/create`}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              إنشاء فاتورة جديدة
            </Link>
          </div>
        </div>
      )}
    </div>
  );
} 