(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[639],{22904:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>m});var t=a(95155),l=a(12115),n=a(27043),i=a(19946);let r=(0,i.A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),c=(0,i.A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var d=a(34869);let o=(0,i.A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);function m(){let e=(0,n.useTranslations)(),[s,a]=(0,l.useState)(null),[i,m]=(0,l.useState)(!0),[x,h]=(0,l.useState)(!1),[p,g]=(0,l.useState)("company");(0,l.useEffect)(()=>{u()},[]);let u=async()=>{try{m(!0);let e=await fetch("/api/settings"),s=await e.json();a(s)}catch(e){console.error("Error fetching settings:",e)}finally{m(!1)}},y=async()=>{if(s)try{h(!0),(await fetch("/api/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)})).ok?alert("تم حفظ الإعدادات بنجاح"):alert("حدث خطأ أثناء حفظ الإعدادات")}catch(e){console.error("Error saving settings:",e),alert("حدث خطأ أثناء حفظ الإعدادات")}finally{h(!1)}};return i?(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)("div",{className:"spinner"}),(0,t.jsx)("span",{className:"ml-2",children:e("common.loading")})]}):s?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e("settings.title")}),(0,t.jsxs)("button",{onClick:y,disabled:x,className:"btn-primary flex items-center space-x-2 rtl:space-x-reverse",children:[(0,t.jsx)(r,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:x?"جاري الحفظ...":e("common.save")})]})]}),(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsxs)("nav",{className:"-mb-px flex space-x-8 rtl:space-x-reverse",children:[(0,t.jsxs)("button",{onClick:()=>g("company"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("company"===p?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,t.jsx)(c,{className:"w-4 h-4 inline-block mr-2"}),e("settings.companyInfo")]}),(0,t.jsxs)("button",{onClick:()=>g("localization"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("localization"===p?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,t.jsx)(d.A,{className:"w-4 h-4 inline-block mr-2"}),"اللغة والعملة"]})]})}),"company"===p&&(0,t.jsxs)("div",{className:"card",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-6",children:e("settings.companyInfo")}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:e("settings.companyLogo")}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[s.logo&&(0,t.jsx)("img",{src:s.logo,alt:"Company Logo",className:"w-16 h-16 object-contain border border-gray-300 rounded"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var t;let l=null==(t=e.target.files)?void 0:t[0];if(l){let e=new FileReader;e.onload=e=>{if(s){var t;a({...s,logo:null==(t=e.target)?void 0:t.result})}},e.readAsDataURL(l)}},className:"hidden",id:"logo-upload"}),(0,t.jsxs)("label",{htmlFor:"logo-upload",className:"btn-secondary cursor-pointer flex items-center space-x-2 rtl:space-x-reverse",children:[(0,t.jsx)(o,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"رفع شعار"})]})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[e("settings.companyName")," (عربي)"]}),(0,t.jsx)("input",{type:"text",value:s.name,onChange:e=>a({...s,name:e.target.value}),className:"input-field"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[e("settings.companyName")," (English)"]}),(0,t.jsx)("input",{type:"text",value:s.nameEn,onChange:e=>a({...s,nameEn:e.target.value}),className:"input-field"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[e("settings.companyAddress")," (عربي)"]}),(0,t.jsx)("textarea",{value:s.address,onChange:e=>a({...s,address:e.target.value}),className:"input-field",rows:3})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[e("settings.companyAddress")," (English)"]}),(0,t.jsx)("textarea",{value:s.addressEn,onChange:e=>a({...s,addressEn:e.target.value}),className:"input-field",rows:3})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:e("settings.companyPhone")}),(0,t.jsx)("input",{type:"tel",value:s.phone,onChange:e=>a({...s,phone:e.target.value}),className:"input-field"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:e("settings.companyEmail")}),(0,t.jsx)("input",{type:"email",value:s.email,onChange:e=>a({...s,email:e.target.value}),className:"input-field"})]}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الموقع الإلكتروني"}),(0,t.jsx)("input",{type:"url",value:s.website,onChange:e=>a({...s,website:e.target.value}),className:"input-field",placeholder:"https://example.com"})]})]})]}),"localization"===p&&(0,t.jsxs)("div",{className:"card",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-6",children:"إعدادات اللغة والعملة"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:e("settings.language")}),(0,t.jsxs)("select",{value:s.language,onChange:e=>a({...s,language:e.target.value}),className:"input-field",children:[(0,t.jsx)("option",{value:"ar",children:"العربية"}),(0,t.jsx)("option",{value:"en",children:"English"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:e("settings.currency")}),(0,t.jsxs)("select",{value:s.currency,onChange:e=>a({...s,currency:e.target.value}),className:"input-field",children:[(0,t.jsx)("option",{value:"LYD",children:e("settings.currencies.LYD")}),(0,t.jsx)("option",{value:"EGP",children:e("settings.currencies.EGP")}),(0,t.jsx)("option",{value:"SAR",children:e("settings.currencies.SAR")})]})]})]}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"ملاحظة:"}),(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"تغيير اللغة سيؤثر على واجهة النظام، بينما تغيير العملة سيؤثر على عرض المبالغ في التقارير والفواتير."})]})]})]}):(0,t.jsx)("div",{children:"خطأ في تحميل الإعدادات"})}},34707:(e,s,a)=>{Promise.resolve().then(a.bind(a,22904))},34869:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[320,441,684,358],()=>s(34707)),_N_E=e.O()}]);