'use client';

import { useTranslations } from 'next-intl';
import { useParams, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { 
  Save, 
  Plus, 
  Trash2, 
  Calculator,
  User,
  Package,
  DollarSign,
  Calendar,
  FileText
} from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  phone: string;
  email: string;
  address: string;
  taxNumber?: string;
}

interface Service {
  id: string;
  name: string;
  price: number;
}

interface InvoiceItem {
  id: string;
  itemCode: string;
  itemName: string;
  length: number;
  width: number;
  quantity: number;
  squareMeters: number;
  unitPrice: number;
  total: number;
  notes?: string;
}

interface InvoiceService {
  id: string;
  serviceId: string;
  serviceName: string;
  quantity: number;
  price: number;
  total: number;
}

export default function CreateInvoicePage() {
  const t = useTranslations('sales');
  const params = useParams();
  const router = useRouter();
  const locale = params.locale as string;

  // State
  const [invoiceNumber, setInvoiceNumber] = useState('S-001');
  const [invoiceDate, setInvoiceDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [items, setItems] = useState<InvoiceItem[]>([]);
  const [invoiceServices, setInvoiceServices] = useState<InvoiceService[]>([]);
  const [discount, setDiscount] = useState(0);
  const [tax, setTax] = useState(0);
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);

  // Calculations
  const subtotal = items.reduce((sum, item) => sum + item.total, 0);
  const servicesTotal = invoiceServices.reduce((sum, service) => sum + service.total, 0);
  const totalBeforeTax = subtotal + servicesTotal - discount;
  const taxAmount = (totalBeforeTax * tax) / 100;
  const total = totalBeforeTax + taxAmount;

  // Mock data loading
  useEffect(() => {
    const mockCustomers: Customer[] = [
      {
        id: '1',
        name: 'أحمد محمد علي',
        phone: '+218-91-1234567',
        email: '<EMAIL>',
        address: 'طرابلس، ليبيا',
        taxNumber: '*********'
      },
      {
        id: '2',
        name: 'فاطمة أحمد حسن',
        phone: '+218-92-2345678',
        email: '<EMAIL>',
        address: 'بنغازي، ليبيا',
        taxNumber: '*********'
      }
    ];

    const mockServices: Service[] = [
      { id: '1', name: 'قص', price: 50 },
      { id: '2', name: 'تركيب', price: 100 },
      { id: '3', name: 'نقل', price: 75 },
      { id: '4', name: 'تلميع', price: 25 }
    ];

    setCustomers(mockCustomers);
    setServices(mockServices);
  }, []);

  // Add new item
  const addItem = () => {
    const newItem: InvoiceItem = {
      id: Date.now().toString(),
      itemCode: '',
      itemName: '',
      length: 0,
      width: 0,
      quantity: 1,
      squareMeters: 0,
      unitPrice: 0,
      total: 0
    };
    setItems([...items, newItem]);
  };

  // Remove item
  const removeItem = (id: string) => {
    setItems(items.filter(item => item.id !== id));
  };

  // Update item
  const updateItem = (id: string, field: keyof InvoiceItem, value: any) => {
    setItems(items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };
        
        // Calculate square meters and total
        if (field === 'length' || field === 'width' || field === 'quantity') {
          updatedItem.squareMeters = (updatedItem.length * updatedItem.width * updatedItem.quantity) / 1000000;
          updatedItem.total = updatedItem.squareMeters * updatedItem.unitPrice;
        }
        
        if (field === 'unitPrice') {
          updatedItem.total = updatedItem.squareMeters * value;
        }
        
        return updatedItem;
      }
      return item;
    }));
  };

  // Add service
  const addService = () => {
    if (services.length === 0) return;
    
    const service = services[0];
    const newService: InvoiceService = {
      id: Date.now().toString(),
      serviceId: service.id,
      serviceName: service.name,
      quantity: 1,
      price: service.price,
      total: service.price
    };
    setInvoiceServices([...invoiceServices, newService]);
  };

  // Remove service
  const removeService = (id: string) => {
    setInvoiceServices(invoiceServices.filter(service => service.id !== id));
  };

  // Update service
  const updateService = (id: string, field: keyof InvoiceService, value: any) => {
    setInvoiceServices(invoiceServices.map(service => {
      if (service.id === id) {
        const updatedService = { ...service, [field]: value };
        
        if (field === 'quantity' || field === 'price') {
          updatedService.total = updatedService.quantity * updatedService.price;
        }
        
        if (field === 'serviceId') {
          const selectedService = services.find(s => s.id === value);
          if (selectedService) {
            updatedService.serviceName = selectedService.name;
            updatedService.price = selectedService.price;
            updatedService.total = updatedService.quantity * selectedService.price;
          }
        }
        
        return updatedService;
      }
      return service;
    }));
  };

  // Save invoice
  const saveInvoice = async (status: 'DRAFT' | 'FINAL') => {
    if (!selectedCustomer) {
      alert('يرجى اختيار العميل');
      return;
    }

    if (items.length === 0 && invoiceServices.length === 0) {
      alert('يرجى إضافة أصناف أو خدمات');
      return;
    }

    setLoading(true);
    
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('Saving invoice:', {
        invoiceNumber,
        invoiceDate,
        customer: selectedCustomer,
        items,
        services: invoiceServices,
        subtotal,
        servicesTotal,
        discount,
        tax,
        total,
        notes,
        status
      });
      
      alert(`تم حفظ الفاتورة بنجاح (${status === 'DRAFT' ? 'مبدئية' : 'نهائية'})`);
      router.push(`/${locale}/sales/invoices`);
    } catch (error) {
      alert('حدث خطأ أثناء حفظ الفاتورة');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">إنشاء فاتورة جديدة</h1>
          <p className="text-gray-600">إدخال بيانات الفاتورة والأصناف والخدمات</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <button
            onClick={() => saveInvoice('DRAFT')}
            disabled={loading}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center"
          >
            <Save className="w-4 h-4 mr-2" />
            حفظ كمسودة
          </button>
          <button
            onClick={() => saveInvoice('FINAL')}
            disabled={loading}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <FileText className="w-4 h-4 mr-2" />
            حفظ نهائي
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Invoice Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">بيانات الفاتورة</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رقم الفاتورة
                </label>
                <input
                  type="text"
                  value={invoiceNumber}
                  onChange={(e) => setInvoiceNumber(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تاريخ الفاتورة
                </label>
                <input
                  type="date"
                  value={invoiceDate}
                  onChange={(e) => setInvoiceDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  العميل
                </label>
                <select
                  value={selectedCustomer?.id || ''}
                  onChange={(e) => {
                    const customer = customers.find(c => c.id === e.target.value);
                    setSelectedCustomer(customer || null);
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">اختر العميل</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>
                      {customer.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Items */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900">الأصناف</h2>
              <button
                onClick={addItem}
                className="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center"
              >
                <Plus className="w-4 h-4 mr-2" />
                إضافة صنف
              </button>
            </div>
            
            {items.length === 0 ? (
              <p className="text-gray-500 text-center py-8">لا توجد أصناف مضافة</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">الكود</th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">الصنف</th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">الطول</th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">العرض</th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">الكمية</th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">م²</th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">السعر/م²</th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">الإجمالي</th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">إجراءات</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {items.map((item) => (
                      <tr key={item.id}>
                        <td className="px-3 py-2">
                          <input
                            type="text"
                            value={item.itemCode}
                            onChange={(e) => updateItem(item.id, 'itemCode', e.target.value)}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="text"
                            value={item.itemName}
                            onChange={(e) => updateItem(item.id, 'itemName', e.target.value)}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="number"
                            value={item.length}
                            onChange={(e) => updateItem(item.id, 'length', parseFloat(e.target.value) || 0)}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="number"
                            value={item.width}
                            onChange={(e) => updateItem(item.id, 'width', parseFloat(e.target.value) || 0)}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="number"
                            value={item.quantity}
                            onChange={(e) => updateItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        </td>
                        <td className="px-3 py-2 text-sm text-gray-900">
                          {item.squareMeters.toFixed(2)}
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="number"
                            value={item.unitPrice}
                            onChange={(e) => updateItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        </td>
                        <td className="px-3 py-2 text-sm font-medium text-gray-900">
                          {item.total.toLocaleString('ar-LY')} د.ل
                        </td>
                        <td className="px-3 py-2">
                          <button
                            onClick={() => removeItem(item.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Services */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900">الخدمات</h2>
              <button
                onClick={addService}
                className="bg-purple-600 text-white px-3 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center"
              >
                <Plus className="w-4 h-4 mr-2" />
                إضافة خدمة
              </button>
            </div>
            
            {invoiceServices.length === 0 ? (
              <p className="text-gray-500 text-center py-8">لا توجد خدمات مضافة</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">الخدمة</th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">الكمية</th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">السعر</th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">الإجمالي</th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">إجراءات</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {invoiceServices.map((service) => (
                      <tr key={service.id}>
                        <td className="px-3 py-2">
                          <select
                            value={service.serviceId}
                            onChange={(e) => updateService(service.id, 'serviceId', e.target.value)}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          >
                            {services.map(s => (
                              <option key={s.id} value={s.id}>{s.name}</option>
                            ))}
                          </select>
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="number"
                            value={service.quantity}
                            onChange={(e) => updateService(service.id, 'quantity', parseFloat(e.target.value) || 0)}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="number"
                            value={service.price}
                            onChange={(e) => updateService(service.id, 'price', parseFloat(e.target.value) || 0)}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        </td>
                        <td className="px-3 py-2 text-sm font-medium text-gray-900">
                          {service.total.toLocaleString('ar-LY')} د.ل
                        </td>
                        <td className="px-3 py-2">
                          <button
                            onClick={() => removeService(service.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Notes */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">ملاحظات</h2>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="أضف ملاحظات للفاتورة..."
            />
          </div>
        </div>

        {/* Invoice Summary */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">ملخص الفاتورة</h2>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">إجمالي الأصناف:</span>
                <span className="font-medium">{subtotal.toLocaleString('ar-LY')} د.ل</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">إجمالي الخدمات:</span>
                <span className="font-medium">{servicesTotal.toLocaleString('ar-LY')} د.ل</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">الخصم:</span>
                <input
                  type="number"
                  value={discount}
                  onChange={(e) => setDiscount(parseFloat(e.target.value) || 0)}
                  className="w-20 text-right border-none bg-transparent font-medium"
                />
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">الضريبة (%):</span>
                <input
                  type="number"
                  value={tax}
                  onChange={(e) => setTax(parseFloat(e.target.value) || 0)}
                  className="w-20 text-right border-none bg-transparent font-medium"
                />
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">مبلغ الضريبة:</span>
                <span className="font-medium">{taxAmount.toLocaleString('ar-LY')} د.ل</span>
              </div>
              
              <hr className="my-3" />
              
              <div className="flex justify-between text-lg font-bold">
                <span>الإجمالي النهائي:</span>
                <span className="text-blue-600">{total.toLocaleString('ar-LY')} د.ل</span>
              </div>
            </div>
          </div>

          {/* Customer Info */}
          {selectedCustomer && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">بيانات العميل</h2>
              
              <div className="space-y-2">
                <div className="flex items-center">
                  <User className="w-4 h-4 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-600">{selectedCustomer.name}</span>
                </div>
                
                <div className="flex items-center">
                  <span className="text-sm text-gray-600">الهاتف: {selectedCustomer.phone}</span>
                </div>
                
                <div className="flex items-center">
                  <span className="text-sm text-gray-600">البريد: {selectedCustomer.email}</span>
                </div>
                
                <div className="flex items-center">
                  <span className="text-sm text-gray-600">العنوان: {selectedCustomer.address}</span>
                </div>
                
                {selectedCustomer.taxNumber && (
                  <div className="flex items-center">
                    <span className="text-sm text-gray-600">الرقم الضريبي: {selectedCustomer.taxNumber}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 