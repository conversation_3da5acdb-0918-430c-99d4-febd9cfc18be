"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-intl";
exports.ids = ["vendor-chunks/use-intl"];
exports.modules = {

/***/ "(rsc)/./node_modules/use-intl/dist/development/core.js":
/*!********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/core.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-QqAaZwGD.js */ \"(rsc)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(rsc)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n\nfunction createTranslatorImpl(_ref, namespacePrefix) {\n  let {\n    messages,\n    namespace,\n    ...rest\n  } = _ref;\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the function invocation.\n  messages = messages[namespacePrefix];\n  namespace = createFormatter.resolveNamespace(namespace, namespacePrefix);\n  return createFormatter.createBaseTranslator({\n    ...rest,\n    messages,\n    namespace\n  });\n}\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction createTranslator(_ref) {\n  let {\n    _cache = initializeConfig.createCache(),\n    _formatters = initializeConfig.createIntlFormatters(_cache),\n    getMessageFallback = initializeConfig.defaultGetMessageFallback,\n    messages,\n    namespace,\n    onError = initializeConfig.defaultOnError,\n    ...rest\n  } = _ref;\n  // We have to wrap the actual function so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  return createTranslatorImpl({\n    ...rest,\n    onError,\n    cache: _cache,\n    formatters: _formatters,\n    getMessageFallback,\n    // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n    messages: {\n      '!': messages\n    },\n    namespace: namespace ? \"!.\".concat(namespace) : '!'\n  }, '!');\n}\n\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createFormatter = createFormatter.createFormatter;\nexports.createTranslator = createTranslator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js":
/*!****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar IntlMessageFormat = __webpack_require__(/*! intl-messageformat */ \"(rsc)/./node_modules/intl-messageformat/lib/index.js\");\nvar React = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar IntlMessageFormat__default = /*#__PURE__*/_interopDefault(IntlMessageFormat);\n\nfunction setTimeZoneInFormats(formats, timeZone) {\n  if (!formats) return formats;\n\n  // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n  // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n  return Object.keys(formats).reduce((acc, key) => {\n    acc[key] = {\n      timeZone,\n      ...formats[key]\n    };\n    return acc;\n  }, {});\n}\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(formats, timeZone) {\n  const formatsWithTimeZone = timeZone ? {\n    ...formats,\n    dateTime: setTimeZoneInFormats(formats.dateTime, timeZone)\n  } : formats;\n  const mfDateDefaults = IntlMessageFormat__default.default.formats.date;\n  const defaultDateFormats = timeZone ? setTimeZoneInFormats(mfDateDefaults, timeZone) : mfDateDefaults;\n  const mfTimeDefaults = IntlMessageFormat__default.default.formats.time;\n  const defaultTimeFormats = timeZone ? setTimeZoneInFormats(mfTimeDefaults, timeZone) : mfTimeDefaults;\n  return {\n    ...formatsWithTimeZone,\n    date: {\n      ...defaultDateFormats,\n      ...formatsWithTimeZone.dateTime\n    },\n    time: {\n      ...defaultTimeFormats,\n      ...formatsWithTimeZone.dateTime\n    }\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = initializeConfig.memoFn(function () {\n    return new IntlMessageFormat__default.default(arguments.length <= 0 ? undefined : arguments[0], arguments.length <= 1 ? undefined : arguments[1], arguments.length <= 2 ? undefined : arguments[2], {\n      formatters: intlFormatters,\n      ...(arguments.length <= 3 ? undefined : arguments[3])\n    });\n  }, cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = initializeConfig.joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(\"No messages available at `\".concat(namespace, \"`.\") );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(\"Could not resolve `\".concat(fullKey, \"` in messages for locale `\").concat(locale, \"`.\") );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  if (Object.keys(values).length === 0) return undefined;\n\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/React.isValidElement(result) ? /*#__PURE__*/React.cloneElement(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace) {\n  let onError = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : initializeConfig.defaultOnError;\n  try {\n    if (!messages) {\n      throw new Error(\"No messages were configured on the provider.\" );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(\"No messages for namespace `\".concat(namespace, \"` found.\") );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  if (values) return undefined;\n  const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n\n  // Placeholders can be in the message if there are default values,\n  // or if the user has forgotten to provide values. In the latter\n  // case we need to compile the message to receive an error.\n  const hasPlaceholders = /<|{/.test(unescapedMessage);\n  if (!hasPlaceholders) {\n    return unescapedMessage;\n  }\n  return undefined;\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl(_ref) {\n  let {\n    cache,\n    defaultTranslationValues,\n    formats: globalFormats,\n    formatters,\n    getMessageFallback = initializeConfig.defaultGetMessageFallback,\n    locale,\n    messagesOrError,\n    namespace,\n    onError,\n    timeZone\n  } = _ref;\n  const hasMessagesError = messagesOrError instanceof initializeConfig.IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new initializeConfig.IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = initializeConfig.IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages\");\n        }\n      } else {\n        code = initializeConfig.IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an object, but only strings are supported. Use a `.` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages\");\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat({\n        ...globalFormats,\n        ...formats\n      }, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? \" (\".concat(thrownError.originalMessage, \")\") : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      prepareTranslationValues({\n        ...defaultTranslationValues,\n        ...values\n      }));\n      if (formattedMessage == null) {\n        throw new Error(\"Unable to format `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : 'messages') );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/React.isValidElement(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, \"The message `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : 'messages', \" didn't resolve to a string. If you want to format rich text, use `t.rich` instead.\") );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n\n    // When only string chunks are provided to the parser, only\n    // strings should be returned here. Note that we need a runtime\n    // check for this since rich text values could be accidentally\n    // inherited from `defaultTranslationValues`.\n    if (typeof result !== 'string') {\n      const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\" );\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch (_unused) {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(_ref) {\n  let {\n    _cache: cache = initializeConfig.createCache(),\n    _formatters: formatters = initializeConfig.createIntlFormatters(cache),\n    formats,\n    locale,\n    now: globalNow,\n    onError = initializeConfig.defaultOnError,\n    timeZone: globalTimeZone\n  } = _ref;\n  function applyTimeZone(options) {\n    var _options;\n    if (!((_options = options) !== null && _options !== void 0 && _options.timeZone)) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `timeZone` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone\" ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats === null || typeFormats === void 0 ? void 0 : typeFormats[formatName];\n      if (!options) {\n        const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_FORMAT, \"Format `\".concat(formatName, \"` is not available. You can configure it on the provider or provide custom options.\") );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions);\n    } catch (_unused) {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(/** If a number is supplied, this is interpreted as a UTC timestamp. */\n  value,\n  /** If a time zone is supplied, the `value` is converted to that time zone.\n   * Otherwise the user time zone will be used. */\n  formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(/** If a number is supplied, this is interpreted as a UTC timestamp. */\n  start, /** If a number is supplied, this is interpreted as a UTC timestamp. */\n  end,\n  /** If a time zone is supplied, the values are converted to that time zone.\n   * Otherwise the user time zone will be used. */\n  formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    if (globalNow) {\n      return globalNow;\n    } else {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `now` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#now\" ));\n      return new Date();\n    }\n  }\n  function relativeTime(/** The date time that needs to be formatted. */\n  date, /** The reference point in time to which `date` will be formatted in relation to.  */\n  nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nexports.createBaseTranslator = createBaseTranslator;\nexports.createFormatter = createFormatter;\nexports.resolveNamespace = resolveNamespace;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar fastMemoize = __webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\n\nlet IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}({});\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    _defineProperty(this, \"code\", void 0);\n    _defineProperty(this, \"originalMessage\", void 0);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nfunction joinPath() {\n  for (var _len = arguments.length, parts = new Array(_len), _key = 0; _key < _len; _key++) {\n    parts[_key] = arguments[_key];\n  }\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return fastMemoize.memoize(fn, {\n    cache: createMemoCache(cache),\n    strategy: fastMemoize.strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return new ConstructorFn(...args);\n  }, cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(_ref => {\n    let [key, messageOrMessages] = _ref;\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += \" (at \".concat(parentPath, \")\");\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, \"Namespace keys can not contain the character \\\".\\\" as this is used to express nesting. Please remove it or replace it with another character.\\n\\nInvalid \".concat(invalidKeyLabels.length === 1 ? 'key' : 'keys', \": \").concat(invalidKeyLabels.join(', '), \"\\n\\nIf you're migrating from a flat structure, you can convert your messages as follows:\\n\\nimport {set} from \\\"lodash\\\";\\n\\nconst input = {\\n  \\\"one.one\\\": \\\"1.1\\\",\\n  \\\"one.two\\\": \\\"1.2\\\",\\n  \\\"two.one.one\\\": \\\"2.1.1\\\"\\n};\\n\\nconst output = Object.entries(input).reduce(\\n  (acc, [key, value]) => set(acc, key, value),\\n  {}\\n);\\n\\n// Output:\\n//\\n// {\\n//   \\\"one\\\": {\\n//     \\\"one\\\": \\\"1.1\\\",\\n//     \\\"two\\\": \\\"1.2\\\"\\n//   },\\n//   \\\"two\\\": {\\n//     \\\"one\\\": {\\n//       \\\"one\\\": \\\"2.1.1\\\"\\n//     }\\n//   }\\n// }\\n\") ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig(_ref) {\n  let {\n    getMessageFallback,\n    messages,\n    onError,\n    ...rest\n  } = _ref;\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    messages,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\nexports.IntlError = IntlError;\nexports.IntlErrorCode = IntlErrorCode;\nexports.createCache = createCache;\nexports.createIntlFormatters = createIntlFormatters;\nexports.defaultGetMessageFallback = defaultGetMessageFallback;\nexports.defaultOnError = defaultOnError;\nexports.initializeConfig = initializeConfig;\nexports.joinPath = joinPath;\nexports.memoFn = memoFn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/_IntlProvider.js":
/*!*****************************************************!*\
  !*** ./node_modules/use-intl/dist/_IntlProvider.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./development/_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fSW50bFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw2SUFBMEQ7QUFDNUQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaG9zc2FcXERvd25sb2Fkc1xcU01BUlRcXG5vZGVfbW9kdWxlc1xcdXNlLWludGxcXGRpc3RcXF9JbnRsUHJvdmlkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vcHJvZHVjdGlvbi9fSW50bFByb3ZpZGVyLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGV2ZWxvcG1lbnQvX0ludGxQcm92aWRlci5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/_IntlProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/_useLocale.js":
/*!**************************************************!*\
  !*** ./node_modules/use-intl/dist/_useLocale.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./development/_useLocale.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fdXNlTG9jYWxlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSx1SUFBdUQ7QUFDekQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaG9zc2FcXERvd25sb2Fkc1xcU01BUlRcXG5vZGVfbW9kdWxlc1xcdXNlLWludGxcXGRpc3RcXF91c2VMb2NhbGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vcHJvZHVjdGlvbi9fdXNlTG9jYWxlLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGV2ZWxvcG1lbnQvX3VzZUxvY2FsZS5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/_useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js":
/*!************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\nconst IntlContext = /*#__PURE__*/React.createContext(undefined);\n\nexports.IntlContext = IntlContext;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9JbnRsQ29udGV4dC1CS2ZzbnpCeC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixZQUFZLG1CQUFPLENBQUMsaUdBQU87O0FBRTNCOztBQUVBLG1CQUFtQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxob3NzYVxcRG93bmxvYWRzXFxTTUFSVFxcbm9kZV9tb2R1bGVzXFx1c2UtaW50bFxcZGlzdFxcZGV2ZWxvcG1lbnRcXEludGxDb250ZXh0LUJLZnNuekJ4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIFJlYWN0ID0gcmVxdWlyZSgncmVhY3QnKTtcblxuY29uc3QgSW50bENvbnRleHQgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlQ29udGV4dCh1bmRlZmluZWQpO1xuXG5leHBvcnRzLkludGxDb250ZXh0ID0gSW50bENvbnRleHQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js":
/*!*****************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_IntlProvider.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar IntlContext = __webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\nfunction IntlProvider(_ref) {\n  let {\n    children,\n    defaultTranslationValues,\n    formats,\n    getMessageFallback,\n    locale,\n    messages,\n    now,\n    onError,\n    timeZone\n  } = _ref;\n  // The formatter cache is released when the locale changes. For\n  // long-running apps with a persistent `IntlProvider` at the root,\n  // this can reduce the memory footprint (e.g. in React Native).\n  const cache = React.useMemo(() => {\n    return initializeConfig.createCache();\n  }, [locale]);\n  const formatters = React.useMemo(() => initializeConfig.createIntlFormatters(cache), [cache]);\n\n  // Memoizing this value helps to avoid triggering a re-render of all\n  // context consumers in case the configuration didn't change. However,\n  // if some of the non-primitive values change, a re-render will still\n  // be triggered. Note that there's no need to put `memo` on `IntlProvider`\n  // itself, because the `children` typically change on every render.\n  // There's some burden on the consumer side if it's important to reduce\n  // re-renders, put that's how React works.\n  // See: https://blog.isquaredsoftware.com/2020/05/blogged-answers-a-mostly-complete-guide-to-react-rendering-behavior/#context-updates-and-render-optimizations\n  const value = React.useMemo(() => ({\n    ...initializeConfig.initializeConfig({\n      locale,\n      defaultTranslationValues,\n      formats,\n      getMessageFallback,\n      messages,\n      now,\n      onError,\n      timeZone\n    }),\n    formatters,\n    cache\n  }), [cache, defaultTranslationValues, formats, formatters, getMessageFallback, locale, messages, now, onError, timeZone]);\n  return /*#__PURE__*/React__default.default.createElement(IntlContext.IntlContext.Provider, {\n    value: value\n  }, children);\n}\n\nexports.IntlProvider = IntlProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js":
/*!***********************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar IntlContext = __webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n\nfunction useIntlContext() {\n  const context = React.useContext(IntlContext.IntlContext);\n  if (!context) {\n    throw new Error('No intl context found. Have you configured the provider? See https://next-intl.dev/docs/usage/configuration#client-server-components' );\n  }\n  return context;\n}\n\nfunction useLocale() {\n  return useIntlContext().locale;\n}\n\nexports.useIntlContext = useIntlContext;\nexports.useLocale = useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLUJLM2pPZWFBLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLFlBQVksbUJBQU8sQ0FBQyxpR0FBTztBQUMzQixrQkFBa0IsbUJBQU8sQ0FBQyx5R0FBMkI7O0FBRXJEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxzQkFBc0I7QUFDdEIsaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhvc3NhXFxEb3dubG9hZHNcXFNNQVJUXFxub2RlX21vZHVsZXNcXHVzZS1pbnRsXFxkaXN0XFxkZXZlbG9wbWVudFxcX3VzZUxvY2FsZS1CSzNqT2VhQS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBSZWFjdCA9IHJlcXVpcmUoJ3JlYWN0Jyk7XG52YXIgSW50bENvbnRleHQgPSByZXF1aXJlKCcuL0ludGxDb250ZXh0LUJLZnNuekJ4LmpzJyk7XG5cbmZ1bmN0aW9uIHVzZUludGxDb250ZXh0KCkge1xuICBjb25zdCBjb250ZXh0ID0gUmVhY3QudXNlQ29udGV4dChJbnRsQ29udGV4dC5JbnRsQ29udGV4dCk7XG4gIGlmICghY29udGV4dCkge1xuICAgIHRocm93IG5ldyBFcnJvcignTm8gaW50bCBjb250ZXh0IGZvdW5kLiBIYXZlIHlvdSBjb25maWd1cmVkIHRoZSBwcm92aWRlcj8gU2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL3VzYWdlL2NvbmZpZ3VyYXRpb24jY2xpZW50LXNlcnZlci1jb21wb25lbnRzJyApO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufVxuXG5mdW5jdGlvbiB1c2VMb2NhbGUoKSB7XG4gIHJldHVybiB1c2VJbnRsQ29udGV4dCgpLmxvY2FsZTtcbn1cblxuZXhwb3J0cy51c2VJbnRsQ29udGV4dCA9IHVzZUludGxDb250ZXh0O1xuZXhwb3J0cy51c2VMb2NhbGUgPSB1c2VMb2NhbGU7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_useLocale.js":
/*!**************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_useLocale.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _useLocale = __webpack_require__(/*! ./_useLocale-BK3jOeaA.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n\n\n\nexports.useLocale = _useLocale.useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0QsaUJBQWlCLG1CQUFPLENBQUMsdUdBQTBCO0FBQ25ELG1CQUFPLENBQUMsaUdBQU87QUFDZixtQkFBTyxDQUFDLHlHQUEyQjs7OztBQUluQyxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaG9zc2FcXERvd25sb2Fkc1xcU01BUlRcXG5vZGVfbW9kdWxlc1xcdXNlLWludGxcXGRpc3RcXGRldmVsb3BtZW50XFxfdXNlTG9jYWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxudmFyIF91c2VMb2NhbGUgPSByZXF1aXJlKCcuL191c2VMb2NhbGUtQkszak9lYUEuanMnKTtcbnJlcXVpcmUoJ3JlYWN0Jyk7XG5yZXF1aXJlKCcuL0ludGxDb250ZXh0LUJLZnNuekJ4LmpzJyk7XG5cblxuXG5leHBvcnRzLnVzZUxvY2FsZSA9IF91c2VMb2NhbGUudXNlTG9jYWxlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/core.js":
/*!********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/core.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-QqAaZwGD.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction createTranslatorImpl(_ref, namespacePrefix) {\n  let {\n    messages,\n    namespace,\n    ...rest\n  } = _ref;\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the function invocation.\n  messages = messages[namespacePrefix];\n  namespace = createFormatter.resolveNamespace(namespace, namespacePrefix);\n  return createFormatter.createBaseTranslator({\n    ...rest,\n    messages,\n    namespace\n  });\n}\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction createTranslator(_ref) {\n  let {\n    _cache = initializeConfig.createCache(),\n    _formatters = initializeConfig.createIntlFormatters(_cache),\n    getMessageFallback = initializeConfig.defaultGetMessageFallback,\n    messages,\n    namespace,\n    onError = initializeConfig.defaultOnError,\n    ...rest\n  } = _ref;\n  // We have to wrap the actual function so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  return createTranslatorImpl({\n    ...rest,\n    onError,\n    cache: _cache,\n    formatters: _formatters,\n    getMessageFallback,\n    // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n    messages: {\n      '!': messages\n    },\n    namespace: namespace ? \"!.\".concat(namespace) : '!'\n  }, '!');\n}\n\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createFormatter = createFormatter.createFormatter;\nexports.createTranslator = createTranslator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js":
/*!****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar IntlMessageFormat = __webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar IntlMessageFormat__default = /*#__PURE__*/_interopDefault(IntlMessageFormat);\n\nfunction setTimeZoneInFormats(formats, timeZone) {\n  if (!formats) return formats;\n\n  // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n  // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n  return Object.keys(formats).reduce((acc, key) => {\n    acc[key] = {\n      timeZone,\n      ...formats[key]\n    };\n    return acc;\n  }, {});\n}\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(formats, timeZone) {\n  const formatsWithTimeZone = timeZone ? {\n    ...formats,\n    dateTime: setTimeZoneInFormats(formats.dateTime, timeZone)\n  } : formats;\n  const mfDateDefaults = IntlMessageFormat__default.default.formats.date;\n  const defaultDateFormats = timeZone ? setTimeZoneInFormats(mfDateDefaults, timeZone) : mfDateDefaults;\n  const mfTimeDefaults = IntlMessageFormat__default.default.formats.time;\n  const defaultTimeFormats = timeZone ? setTimeZoneInFormats(mfTimeDefaults, timeZone) : mfTimeDefaults;\n  return {\n    ...formatsWithTimeZone,\n    date: {\n      ...defaultDateFormats,\n      ...formatsWithTimeZone.dateTime\n    },\n    time: {\n      ...defaultTimeFormats,\n      ...formatsWithTimeZone.dateTime\n    }\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = initializeConfig.memoFn(function () {\n    return new IntlMessageFormat__default.default(arguments.length <= 0 ? undefined : arguments[0], arguments.length <= 1 ? undefined : arguments[1], arguments.length <= 2 ? undefined : arguments[2], {\n      formatters: intlFormatters,\n      ...(arguments.length <= 3 ? undefined : arguments[3])\n    });\n  }, cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = initializeConfig.joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(\"No messages available at `\".concat(namespace, \"`.\") );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(\"Could not resolve `\".concat(fullKey, \"` in messages for locale `\").concat(locale, \"`.\") );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  if (Object.keys(values).length === 0) return undefined;\n\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/React.isValidElement(result) ? /*#__PURE__*/React.cloneElement(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace) {\n  let onError = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : initializeConfig.defaultOnError;\n  try {\n    if (!messages) {\n      throw new Error(\"No messages were configured on the provider.\" );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(\"No messages for namespace `\".concat(namespace, \"` found.\") );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  if (values) return undefined;\n  const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n\n  // Placeholders can be in the message if there are default values,\n  // or if the user has forgotten to provide values. In the latter\n  // case we need to compile the message to receive an error.\n  const hasPlaceholders = /<|{/.test(unescapedMessage);\n  if (!hasPlaceholders) {\n    return unescapedMessage;\n  }\n  return undefined;\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl(_ref) {\n  let {\n    cache,\n    defaultTranslationValues,\n    formats: globalFormats,\n    formatters,\n    getMessageFallback = initializeConfig.defaultGetMessageFallback,\n    locale,\n    messagesOrError,\n    namespace,\n    onError,\n    timeZone\n  } = _ref;\n  const hasMessagesError = messagesOrError instanceof initializeConfig.IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new initializeConfig.IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = initializeConfig.IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages\");\n        }\n      } else {\n        code = initializeConfig.IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an object, but only strings are supported. Use a `.` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages\");\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat({\n        ...globalFormats,\n        ...formats\n      }, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? \" (\".concat(thrownError.originalMessage, \")\") : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      prepareTranslationValues({\n        ...defaultTranslationValues,\n        ...values\n      }));\n      if (formattedMessage == null) {\n        throw new Error(\"Unable to format `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : 'messages') );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/React.isValidElement(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, \"The message `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : 'messages', \" didn't resolve to a string. If you want to format rich text, use `t.rich` instead.\") );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n\n    // When only string chunks are provided to the parser, only\n    // strings should be returned here. Note that we need a runtime\n    // check for this since rich text values could be accidentally\n    // inherited from `defaultTranslationValues`.\n    if (typeof result !== 'string') {\n      const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\" );\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch (_unused) {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(_ref) {\n  let {\n    _cache: cache = initializeConfig.createCache(),\n    _formatters: formatters = initializeConfig.createIntlFormatters(cache),\n    formats,\n    locale,\n    now: globalNow,\n    onError = initializeConfig.defaultOnError,\n    timeZone: globalTimeZone\n  } = _ref;\n  function applyTimeZone(options) {\n    var _options;\n    if (!((_options = options) !== null && _options !== void 0 && _options.timeZone)) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `timeZone` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone\" ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats === null || typeFormats === void 0 ? void 0 : typeFormats[formatName];\n      if (!options) {\n        const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_FORMAT, \"Format `\".concat(formatName, \"` is not available. You can configure it on the provider or provide custom options.\") );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions);\n    } catch (_unused) {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(/** If a number is supplied, this is interpreted as a UTC timestamp. */\n  value,\n  /** If a time zone is supplied, the `value` is converted to that time zone.\n   * Otherwise the user time zone will be used. */\n  formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(/** If a number is supplied, this is interpreted as a UTC timestamp. */\n  start, /** If a number is supplied, this is interpreted as a UTC timestamp. */\n  end,\n  /** If a time zone is supplied, the values are converted to that time zone.\n   * Otherwise the user time zone will be used. */\n  formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    if (globalNow) {\n      return globalNow;\n    } else {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `now` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#now\" ));\n      return new Date();\n    }\n  }\n  function relativeTime(/** The date time that needs to be formatted. */\n  date, /** The reference point in time to which `date` will be formatted in relation to.  */\n  nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nexports.createBaseTranslator = createBaseTranslator;\nexports.createFormatter = createFormatter;\nexports.resolveNamespace = resolveNamespace;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar core = __webpack_require__(/*! ./core.js */ \"(ssr)/./node_modules/use-intl/dist/development/core.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-QqAaZwGD.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\");\nvar _IntlProvider = __webpack_require__(/*! ./_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\nvar react = __webpack_require__(/*! ./react.js */ \"(ssr)/./node_modules/use-intl/dist/development/react.js\");\nvar _useLocale = __webpack_require__(/*! ./_useLocale-BK3jOeaA.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n\n\n\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createTranslator = core.createTranslator;\nexports.createFormatter = createFormatter.createFormatter;\nexports.IntlProvider = _IntlProvider.IntlProvider;\nexports.useFormatter = react.useFormatter;\nexports.useMessages = react.useMessages;\nexports.useNow = react.useNow;\nexports.useTimeZone = react.useTimeZone;\nexports.useTranslations = react.useTranslations;\nexports.useLocale = _useLocale.useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar fastMemoize = __webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\n\nlet IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}({});\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    _defineProperty(this, \"code\", void 0);\n    _defineProperty(this, \"originalMessage\", void 0);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nfunction joinPath() {\n  for (var _len = arguments.length, parts = new Array(_len), _key = 0; _key < _len; _key++) {\n    parts[_key] = arguments[_key];\n  }\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return fastMemoize.memoize(fn, {\n    cache: createMemoCache(cache),\n    strategy: fastMemoize.strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return new ConstructorFn(...args);\n  }, cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(_ref => {\n    let [key, messageOrMessages] = _ref;\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += \" (at \".concat(parentPath, \")\");\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, \"Namespace keys can not contain the character \\\".\\\" as this is used to express nesting. Please remove it or replace it with another character.\\n\\nInvalid \".concat(invalidKeyLabels.length === 1 ? 'key' : 'keys', \": \").concat(invalidKeyLabels.join(', '), \"\\n\\nIf you're migrating from a flat structure, you can convert your messages as follows:\\n\\nimport {set} from \\\"lodash\\\";\\n\\nconst input = {\\n  \\\"one.one\\\": \\\"1.1\\\",\\n  \\\"one.two\\\": \\\"1.2\\\",\\n  \\\"two.one.one\\\": \\\"2.1.1\\\"\\n};\\n\\nconst output = Object.entries(input).reduce(\\n  (acc, [key, value]) => set(acc, key, value),\\n  {}\\n);\\n\\n// Output:\\n//\\n// {\\n//   \\\"one\\\": {\\n//     \\\"one\\\": \\\"1.1\\\",\\n//     \\\"two\\\": \\\"1.2\\\"\\n//   },\\n//   \\\"two\\\": {\\n//     \\\"one\\\": {\\n//       \\\"one\\\": \\\"2.1.1\\\"\\n//     }\\n//   }\\n// }\\n\") ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig(_ref) {\n  let {\n    getMessageFallback,\n    messages,\n    onError,\n    ...rest\n  } = _ref;\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    messages,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\nexports.IntlError = IntlError;\nexports.IntlErrorCode = IntlErrorCode;\nexports.createCache = createCache;\nexports.createIntlFormatters = createIntlFormatters;\nexports.defaultGetMessageFallback = defaultGetMessageFallback;\nexports.defaultOnError = defaultOnError;\nexports.initializeConfig = initializeConfig;\nexports.joinPath = joinPath;\nexports.memoFn = memoFn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/react.js":
/*!*********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/react.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _IntlProvider = __webpack_require__(/*! ./_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\nvar _useLocale = __webpack_require__(/*! ./_useLocale-BK3jOeaA.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-QqAaZwGD.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nlet hasWarnedForMissingTimezone = false;\nconst isServer = typeof window === 'undefined';\nfunction useTranslationsImpl(allMessagesPrefixed, namespacePrefixed, namespacePrefix) {\n  const {\n    cache,\n    defaultTranslationValues,\n    formats: globalFormats,\n    formatters,\n    getMessageFallback,\n    locale,\n    onError,\n    timeZone\n  } = _useLocale.useIntlContext();\n\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the hook invocation.\n  const allMessages = allMessagesPrefixed[namespacePrefix];\n  const namespace = createFormatter.resolveNamespace(namespacePrefixed, namespacePrefix);\n  if (!timeZone && !hasWarnedForMissingTimezone && isServer) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    hasWarnedForMissingTimezone = true;\n    onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"There is no `timeZone` configured, this can lead to markup mismatches caused by environment differences. Consider adding a global default: https://next-intl.dev/docs/configuration#time-zone\" ));\n  }\n  const translate = React.useMemo(() => createFormatter.createBaseTranslator({\n    cache,\n    formatters,\n    getMessageFallback,\n    messages: allMessages,\n    defaultTranslationValues,\n    namespace,\n    onError,\n    formats: globalFormats,\n    locale,\n    timeZone\n  }), [cache, formatters, getMessageFallback, allMessages, defaultTranslationValues, namespace, onError, globalFormats, locale, timeZone]);\n  return translate;\n}\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction useTranslations(namespace) {\n  const context = _useLocale.useIntlContext();\n  const messages = context.messages;\n\n  // We have to wrap the actual hook so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  return useTranslationsImpl({\n    '!': messages\n  },\n  // @ts-expect-error\n  namespace ? \"!.\".concat(namespace) : '!', '!');\n}\n\nfunction getNow() {\n  return new Date();\n}\n\n/**\n * Reading the current date via `new Date()` in components should be avoided, as\n * it causes components to be impure and can lead to flaky tests. Instead, this\n * hook can be used.\n *\n * By default, it returns the time when the component mounts. If `updateInterval`\n * is specified, the value will be updated based on the interval.\n *\n * You can however also return a static value from this hook, if you\n * configure the `now` parameter on the context provider. Note however,\n * that if `updateInterval` is configured in this case, the component\n * will initialize with the global value, but will afterwards update\n * continuously based on the interval.\n *\n * For unit tests, this can be mocked to a constant value. For end-to-end\n * testing, an environment parameter can be passed to the `now` parameter\n * of the provider to mock this to a static value.\n */\nfunction useNow(options) {\n  const updateInterval = options === null || options === void 0 ? void 0 : options.updateInterval;\n  const {\n    now: globalNow\n  } = _useLocale.useIntlContext();\n  const [now, setNow] = React.useState(globalNow || getNow());\n  React.useEffect(() => {\n    if (!updateInterval) return;\n    const intervalId = setInterval(() => {\n      setNow(getNow());\n    }, updateInterval);\n    return () => {\n      clearInterval(intervalId);\n    };\n  }, [globalNow, updateInterval]);\n  return updateInterval == null && globalNow ? globalNow : now;\n}\n\nfunction useTimeZone() {\n  return _useLocale.useIntlContext().timeZone;\n}\n\nfunction useMessages() {\n  const context = _useLocale.useIntlContext();\n  if (!context.messages) {\n    throw new Error('No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages' );\n  }\n  return context.messages;\n}\n\nfunction useFormatter() {\n  const {\n    formats,\n    formatters,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone\n  } = _useLocale.useIntlContext();\n  return React.useMemo(() => createFormatter.createFormatter({\n    formats,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone,\n    _formatters: formatters\n  }), [formats, formatters, globalNow, locale, onError, timeZone]);\n}\n\nexports.IntlProvider = _IntlProvider.IntlProvider;\nexports.useLocale = _useLocale.useLocale;\nexports.useFormatter = useFormatter;\nexports.useMessages = useMessages;\nexports.useNow = useNow;\nexports.useTimeZone = useTimeZone;\nexports.useTranslations = useTranslations;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/react.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/index.js":
/*!*********************************************!*\
  !*** ./node_modules/use-intl/dist/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./development/index.js */ \"(ssr)/./node_modules/use-intl/dist/development/index.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixJQUFJLEtBQXFDLEVBQUUsRUFFMUMsQ0FBQztBQUNGLEVBQUUsNkhBQWtEO0FBQ3BEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhvc3NhXFxEb3dubG9hZHNcXFNNQVJUXFxub2RlX21vZHVsZXNcXHVzZS1pbnRsXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9wcm9kdWN0aW9uL2luZGV4LmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGV2ZWxvcG1lbnQvaW5kZXguanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/index.js\n");

/***/ })

};
;