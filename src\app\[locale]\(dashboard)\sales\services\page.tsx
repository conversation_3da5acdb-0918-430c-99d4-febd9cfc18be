'use client';

import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Package, 
  Edit, 
  Trash2,
  DollarSign
} from 'lucide-react';

interface Service {
  id: string;
  name: string;
  nameEn?: string;
  price: number;
  isActive: boolean;
  createdAt: string;
}

export default function ServicesPage() {
  const t = useTranslations('sales');
  const params = useParams();
  const locale = params.locale as string;
  
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);

  // Mock data loading
  useEffect(() => {
    const mockServices: Service[] = [
      {
        id: '1',
        name: 'قص',
        nameEn: 'Cutting',
        price: 50.00,
        isActive: true,
        createdAt: '2024-01-15'
      },
      {
        id: '2',
        name: 'تركيب',
        nameEn: 'Installation',
        price: 100.00,
        isActive: true,
        createdAt: '2024-01-16'
      },
      {
        id: '3',
        name: 'نقل',
        nameEn: 'Transportation',
        price: 75.00,
        isActive: true,
        createdAt: '2024-01-17'
      },
      {
        id: '4',
        name: 'تلميع',
        nameEn: 'Polishing',
        price: 25.00,
        isActive: true,
        createdAt: '2024-01-18'
      },
      {
        id: '5',
        name: 'تغليف',
        nameEn: 'Packaging',
        price: 30.00,
        isActive: false,
        createdAt: '2024-01-19'
      }
    ];
    
    setTimeout(() => {
      setServices(mockServices);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredServices = services.filter(service =>
    service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.nameEn?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t('services')}</h1>
          <p className="text-gray-600">إدارة الخدمات والأسعار</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
        >
          <Plus className="w-4 h-4 mr-2" />
          {t('addService')}
        </button>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="البحث في الخدمات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Services Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredServices.map((service) => (
          <div key={service.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center">
                <Package className="w-6 h-6 text-blue-500 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{service.name}</h3>
                  {service.nameEn && (
                    <p className="text-sm text-gray-500">{service.nameEn}</p>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <button 
                  onClick={() => setEditingService(service)}
                  className="text-blue-600 hover:text-blue-900"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button className="text-red-600 hover:text-red-900">
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <DollarSign className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-lg font-bold text-gray-900">
                  {service.price.toLocaleString('ar-LY')} د.ل
                </span>
              </div>
              
              {service.isActive ? (
                <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">نشط</span>
              ) : (
                <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">غير نشط</span>
              )}
            </div>
            
            <div className="mt-4 text-xs text-gray-500">
              تاريخ الإنشاء: {new Date(service.createdAt).toLocaleDateString('ar-LY')}
            </div>
          </div>
        ))}
      </div>

      {filteredServices.length === 0 && (
        <div className="text-center py-12">
          <Package className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد خدمات</h3>
          <p className="mt-1 text-sm text-gray-500">ابدأ بإضافة خدمة جديدة</p>
          <div className="mt-6">
            <button
              onClick={() => setShowAddModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              إضافة خدمة جديدة
            </button>
          </div>
        </div>
      )}

      {/* Add/Edit Service Modal */}
      {(showAddModal || editingService) && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingService ? 'تعديل الخدمة' : 'إضافة خدمة جديدة'}
              </h3>
              
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم الخدمة
                  </label>
                  <input
                    type="text"
                    defaultValue={editingService?.name || ''}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم الخدمة بالإنجليزية
                  </label>
                  <input
                    type="text"
                    defaultValue={editingService?.nameEn || ''}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    السعر
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      step="0.01"
                      defaultValue={editingService?.price || ''}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">د.ل</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    defaultChecked={editingService?.isActive ?? true}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="mr-2 block text-sm text-gray-900">
                    نشط
                  </label>
                </div>
                
                <div className="flex justify-end space-x-2 space-x-reverse pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddModal(false);
                      setEditingService(null);
                    }}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {editingService ? 'تحديث' : 'إضافة'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 