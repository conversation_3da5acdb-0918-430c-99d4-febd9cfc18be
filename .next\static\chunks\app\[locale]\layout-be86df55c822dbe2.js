(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[450],{6690:(e,t,r)=>{"use strict";function a(e,t){var r=t&&t.cache?t.cache:s,a=t&&t.serializer?t.serializer:l;return(t&&t.strategy?t.strategy:function(e,t){var r,a,l=1===e.length?n:i;return r=t.cache.create(),a=t.serializer,l.bind(this,e,r,a)})(e,{cache:r,serializer:a})}function n(e,t,r,a){var n=null==a||"number"==typeof a||"boolean"==typeof a?a:r(a),i=t.get(n);return void 0===i&&(i=e.call(this,a),t.set(n,i)),i}function i(e,t,r){var a=Array.prototype.slice.call(arguments,3),n=r(a),i=t.get(n);return void 0===i&&(i=e.apply(this,a),t.set(n,i)),i}r.r(t),r.d(t,{memoize:()=>a,strategies:()=>c});var l=function(){return JSON.stringify(arguments)},o=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),s={create:function(){return new o}},c={variadic:function(e,t){var r,a;return r=t.cache.create(),a=t.serializer,i.bind(this,e,r,a)},monadic:function(e,t){var r,a;return r=t.cache.create(),a=t.serializer,n.bind(this,e,r,a)}}},30347:()=>{},61787:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=r(12115),n=r(79035),i=r(91684);r(6690);var l=function(e){return e&&e.__esModule?e:{default:e}}(a);t.IntlProvider=function(e){let{children:t,defaultTranslationValues:r,formats:o,getMessageFallback:s,locale:c,messages:u,now:m,onError:f,timeZone:g}=e,d=a.useMemo(()=>n.createCache(),[c]),h=a.useMemo(()=>n.createIntlFormatters(d),[d]),v=a.useMemo(()=>({...n.initializeConfig({locale:c,defaultTranslationValues:r,formats:o,getMessageFallback:s,messages:u,now:m,onError:f,timeZone:g}),formatters:h,cache:d}),[d,r,o,h,s,c,u,m,f,g]);return l.default.createElement(i.IntlContext.Provider,{value:v},t)}},78412:(e,t,r)=>{Promise.resolve().then(r.bind(r,82103)),Promise.resolve().then(r.t.bind(r,30347,23))},79035:(e,t,r)=>{"use strict";var a=r(6690);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(".")}function i(e){return n(e.namespace,e.key)}function l(e){console.error(e)}function o(e,t){return a.memoize(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:a.strategies.variadic})}function s(e,t){return o(function(){for(var t=arguments.length,r=Array(t),a=0;a<t;a++)r[a]=arguments[a];return new e(...r)},t)}t.createCache=function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}},t.createIntlFormatters=function(e){return{getDateTimeFormat:s(Intl.DateTimeFormat,e.dateTime),getNumberFormat:s(Intl.NumberFormat,e.number),getPluralRules:s(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:s(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:s(Intl.ListFormat,e.list),getDisplayNames:s(Intl.DisplayNames,e.displayNames)}},t.defaultGetMessageFallback=i,t.defaultOnError=l,t.initializeConfig=function(e){let{getMessageFallback:t,messages:r,onError:a,...n}=e;return{...n,messages:r,onError:a||l,getMessageFallback:t||i}},t.joinPath=n,t.memoFn=o},82103:(e,t,r)=>{"use strict";function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(null,arguments)}r.d(t,{default:()=>l});var n=r(12115),i=r(61787);function l(e){let{locale:t,...r}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl.dev/docs/configuration#locale");return n.createElement(i.IntlProvider,a({locale:t},r))}},91684:(e,t,r)=>{"use strict";t.IntlContext=r(12115).createContext(void 0)}},e=>{var t=t=>e(e.s=t);e.O(0,[690,441,684,358],()=>t(78412)),_N_E=e.O()}]);