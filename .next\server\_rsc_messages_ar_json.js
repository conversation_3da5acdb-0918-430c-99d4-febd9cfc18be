"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_messages_ar_json";
exports.ids = ["_rsc_messages_ar_json"];
exports.modules = {

/***/ "(rsc)/./messages/ar.json":
/*!**************************!*\
  !*** ./messages/ar.json ***!
  \**************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"common":{"save":"حفظ","cancel":"إلغاء","delete":"حذف","edit":"تعديل","add":"إضافة","search":"بحث","loading":"جاري التحميل...","error":"خطأ","success":"تم بنجاح","confirm":"تأكيد","yes":"نعم","no":"لا","close":"إغلاق","print":"طباعة","export":"تصدير","import":"استيراد","total":"الإجمالي","date":"التاريخ","description":"الوصف","amount":"المبلغ","balance":"الرصيد","code":"الكود","name":"الاسم","type":"النوع","status":"الحالة","active":"نشط","inactive":"غير نشط"},"navigation":{"dashboard":"لوحة التحكم","accounts":"الحسابات","sales":"المبيعات","purchases":"المشتريات","inventory":"المخازن","payroll":"الرواتب والأجور","payments":"المدفوعات","receipts":"المقبوضات","reports":"التقارير","settings":"الإعدادات"},"dashboard":{"title":"لوحة التحكم","welcome":"مرحباً بك في نظام سمارت ERP","totalSales":"إجمالي المبيعات","totalPurchases":"إجمالي المشتريات","totalExpenses":"إجمالي المصروفات","netProfit":"صافي الربح","cashFlow":"التدفق النقدي","accountsReceivable":"العملاء","accountsPayable":"الموردون","inventory":"المخزون","salesChart":"مخطط المبيعات","expensesChart":"مخطط المصروفات","monthlyComparison":"مقارنة شهرية","recentTransactions":"المعاملات الأخيرة"},"accounts":{"title":"الحسابات","chartOfAccounts":"شجرة الحسابات","journalEntries":"القيود اليومية","financialStatements":"القوائم المالية","trialBalance":"ميزان المراجعة","incomeStatement":"قائمة الدخل","balanceSheet":"قائمة المركز المالي","addAccount":"إضافة حساب","editAccount":"تعديل حساب","deleteAccount":"حذف حساب","accountCode":"كود الحساب","accountName":"اسم الحساب","accountType":"نوع الحساب","parentAccount":"الحساب الأب","level":"المستوى","debit":"مدين","credit":"دائن","journalNumber":"رقم القيد","reference":"المرجع","addJournalEntry":"إضافة قيد يومية","postEntry":"ترحيل القيد","unpostEntry":"إلغاء الترحيل"},"accountTypes":{"ASSET":"أصول","LIABILITY":"خصوم","EQUITY":"حقوق ملكية","REVENUE":"إيرادات","EXPENSE":"مصروفات"},"modules":{"sales":{"title":"المبيعات","underDevelopment":"هذا الموديول تحت التطوير","comingSoon":"قريباً...","invoices":"الفواتير","customers":"العملاء","services":"الخدمات","reports":"التقارير","settings":"الإعدادات"},"purchases":{"title":"المشتريات","underDevelopment":"هذا الموديول تحت التطوير","comingSoon":"قريباً..."},"inventory":{"title":"المخازن","underDevelopment":"هذا الموديول تحت التطوير","comingSoon":"قريباً..."},"payroll":{"title":"الرواتب والأجور","underDevelopment":"هذا الموديول تحت التطوير","comingSoon":"قريباً..."},"payments":{"title":"المدفوعات","underDevelopment":"هذا الموديول تحت التطوير","comingSoon":"قريباً..."},"receipts":{"title":"المقبوضات","underDevelopment":"هذا الموديول تحت التطوير","comingSoon":"قريباً..."}},"sales":{"title":"المبيعات","invoices":"الفواتير","customers":"العملاء","services":"الخدمات","reports":"التقارير","settings":"الإعدادات","createInvoice":"إنشاء فاتورة","invoiceList":"قائمة الفواتير","customerList":"قائمة العملاء","serviceList":"قائمة الخدمات","addCustomer":"إضافة عميل","addService":"إضافة خدمة","invoiceNumber":"رقم الفاتورة","invoiceDate":"تاريخ الفاتورة","customer":"العميل","status":"الحالة","subtotal":"إجمالي الأصناف","servicesTotal":"إجمالي الخدمات","discount":"الخصم","tax":"الضريبة","total":"الإجمالي","paid":"مسدد","remaining":"المتبقي","notes":"ملاحظات","actions":"الإجراءات","view":"عرض","edit":"تعديل","print":"طباعة","delete":"حذف","save":"حفظ","cancel":"إلغاء","draft":"مبدئية","final":"نهائية","cancelled":"ملغاة","posted":"مرحل","notPosted":"غير مرحل","paymentStatus":"حالة السداد","partial":"مسدد جزئياً","unpaid":"غير مسدد","paymentMethod":"طريقة الدفع","cash":"نقداً","check":"شيك","transfer":"تحويل بنكي","credit":"آجل","itemCode":"كود الصنف","itemName":"اسم الصنف","length":"الطول","width":"العرض","quantity":"الكمية","squareMeters":"م²","unitPrice":"السعر/م²","itemTotal":"إجمالي الصف","serviceName":"اسم الخدمة","servicePrice":"سعر الخدمة","serviceQuantity":"الكمية","serviceTotal":"إجمالي الخدمة","customerName":"اسم العميل","customerPhone":"رقم الهاتف","customerEmail":"البريد الإلكتروني","customerAddress":"العنوان","customerTaxNumber":"الرقم الضريبي","addItem":"إضافة صنف","removeItem":"حذف الصنف","removeService":"حذف الخدمة","calculateTotals":"حساب الإجماليات","postInvoice":"ترحيل الفاتورة","unpostInvoice":"إلغاء الترحيل","cancelInvoice":"إلغاء الفاتورة","addPayment":"إضافة سداد","viewPayments":"عرض السدادات","customerStatement":"كشف حساب العميل","salesReport":"تقرير المبيعات","serviceReport":"تقرير الخدمات","searchInvoices":"البحث في الفواتير","filterByStatus":"تصفية حسب الحالة","filterByDate":"تصفية حسب التاريخ","exportToPDF":"تصدير PDF","exportToExcel":"تصدير Excel","journalSettings":"إعدادات القيود المحاسبية","salesEntry":"قيد البيع","returnEntry":"قيد المرتجع","paymentEntry":"قيد السداد","debitAccount":"الحساب المدين","creditAccount":"الحساب الدائن","autoPosting":"الترحيل التلقائي","manualPosting":"الترحيل اليدوي"},"settings":{"title":"الإعدادات","companyInfo":"بيانات الشركة","companyName":"اسم الشركة","companyAddress":"عنوان الشركة","companyPhone":"هاتف الشركة","companyEmail":"بريد الشركة الإلكتروني","companyLogo":"شعار الشركة","language":"اللغة","currency":"العملة","currencies":{"LYD":"دينار ليبي","EGP":"جنيه مصري","SAR":"ريال سعودي"}},"errors":{"required":"هذا الحقل مطلوب","invalidEmail":"البريد الإلكتروني غير صحيح","invalidNumber":"الرقم غير صحيح","minLength":"الحد الأدنى {min} أحرف","maxLength":"الحد الأقصى {max} حرف","accountCodeExists":"كود الحساب موجود مسبقاً","cannotDeleteAccount":"لا يمكن حذف الحساب لوجود حركات عليه"}}');

/***/ })

};
;