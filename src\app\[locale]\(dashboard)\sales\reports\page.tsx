'use client';

import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { 
  BarChart3, 
  FileText, 
  Download, 
  Calendar,
  Filter,
  TrendingUp,
  Users,
  Package
} from 'lucide-react';

interface SalesReport {
  period: string;
  totalSales: number;
  totalInvoices: number;
  averageInvoice: number;
  topCustomer: string;
  topService: string;
}

interface CustomerReport {
  customerName: string;
  totalInvoices: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  lastInvoiceDate: string;
}

interface ServiceReport {
  serviceName: string;
  totalQuantity: number;
  totalAmount: number;
  averagePrice: number;
  usageCount: number;
}

export default function ReportsPage() {
  const t = useTranslations('sales');
  const params = useParams();
  const locale = params.locale as string;
  
  const [activeTab, setActiveTab] = useState('sales');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [loading, setLoading] = useState(false);

  // Mock data
  const [salesData] = useState<SalesReport[]>([
    {
      period: 'يناير 2024',
      totalSales: 45000,
      totalInvoices: 25,
      averageInvoice: 1800,
      topCustomer: 'أحمد محمد علي',
      topService: 'تركيب'
    },
    {
      period: 'فبراير 2024',
      totalSales: 52000,
      totalInvoices: 30,
      averageInvoice: 1733,
      topCustomer: 'فاطمة أحمد حسن',
      topService: 'قص'
    }
  ]);

  const [customersData] = useState<CustomerReport[]>([
    {
      customerName: 'أحمد محمد علي',
      totalInvoices: 8,
      totalAmount: 15000,
      paidAmount: 12000,
      remainingAmount: 3000,
      lastInvoiceDate: '2024-01-15'
    },
    {
      customerName: 'فاطمة أحمد حسن',
      totalInvoices: 12,
      totalAmount: 23000,
      paidAmount: 20000,
      remainingAmount: 3000,
      lastInvoiceDate: '2024-01-20'
    }
  ]);

  const [servicesData] = useState<ServiceReport[]>([
    {
      serviceName: 'قص',
      totalQuantity: 45,
      totalAmount: 2250,
      averagePrice: 50,
      usageCount: 15
    },
    {
      serviceName: 'تركيب',
      totalQuantity: 20,
      totalAmount: 2000,
      averagePrice: 100,
      usageCount: 8
    },
    {
      serviceName: 'نقل',
      totalQuantity: 30,
      totalAmount: 2250,
      averagePrice: 75,
      usageCount: 12
    }
  ]);

  const generateReport = async () => {
    setLoading(true);
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Generating report for:', { dateFrom, dateTo, activeTab });
    } catch (error) {
      alert('حدث خطأ أثناء توليد التقرير');
    } finally {
      setLoading(false);
    }
  };

  const exportReport = (type: 'pdf' | 'excel') => {
    alert(`سيتم تصدير التقرير بصيغة ${type.toUpperCase()}`);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t('reports')}</h1>
          <p className="text-gray-600">تقارير المبيعات والإحصائيات</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <button
            onClick={() => exportReport('excel')}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center"
          >
            <Download className="w-4 h-4 mr-2" />
            تصدير Excel
          </button>
          <button
            onClick={() => exportReport('pdf')}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center"
          >
            <FileText className="w-4 h-4 mr-2" />
            تصدير PDF
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              من تاريخ
            </label>
            <input
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              إلى تاريخ
            </label>
            <input
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-end">
            <button
              onClick={generateReport}
              disabled={loading}
              className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
            >
              <Filter className="w-4 h-4 mr-2" />
              {loading ? 'جاري التحميل...' : 'توليد التقرير'}
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 space-x-reverse px-6">
            <button
              onClick={() => setActiveTab('sales')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'sales'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center">
                <TrendingUp className="w-4 h-4 mr-2" />
                تقرير المبيعات
              </div>
            </button>
            <button
              onClick={() => setActiveTab('customers')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'customers'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center">
                <Users className="w-4 h-4 mr-2" />
                تقرير العملاء
              </div>
            </button>
            <button
              onClick={() => setActiveTab('services')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'services'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center">
                <Package className="w-4 h-4 mr-2" />
                تقرير الخدمات
              </div>
            </button>
          </nav>
        </div>

        <div className="p-6">
          {/* Sales Report */}
          {activeTab === 'sales' && (
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">تقرير المبيعات</h2>
              
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <DollarSign className="w-8 h-8 text-blue-600" />
                    <div className="mr-3">
                      <p className="text-sm font-medium text-blue-600">إجمالي المبيعات</p>
                      <p className="text-2xl font-bold text-blue-900">
                        {salesData.reduce((sum, item) => sum + item.totalSales, 0).toLocaleString('ar-LY')} د.ل
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-green-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <FileText className="w-8 h-8 text-green-600" />
                    <div className="mr-3">
                      <p className="text-sm font-medium text-green-600">عدد الفواتير</p>
                      <p className="text-2xl font-bold text-green-900">
                        {salesData.reduce((sum, item) => sum + item.totalInvoices, 0)}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-purple-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <BarChart3 className="w-8 h-8 text-purple-600" />
                    <div className="mr-3">
                      <p className="text-sm font-medium text-purple-600">متوسط الفاتورة</p>
                      <p className="text-2xl font-bold text-purple-900">
                        {(salesData.reduce((sum, item) => sum + item.averageInvoice, 0) / salesData.length).toLocaleString('ar-LY')} د.ل
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-orange-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <Users className="w-8 h-8 text-orange-600" />
                    <div className="mr-3">
                      <p className="text-sm font-medium text-orange-600">أفضل عميل</p>
                      <p className="text-lg font-bold text-orange-900">أحمد محمد علي</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Detailed Table */}
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الفترة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجمالي المبيعات</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">عدد الفواتير</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">متوسط الفاتورة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">أفضل عميل</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">أفضل خدمة</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {salesData.map((item, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.period}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.totalSales.toLocaleString('ar-LY')} د.ل</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.totalInvoices}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.averageInvoice.toLocaleString('ar-LY')} د.ل</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.topCustomer}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.topService}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Customers Report */}
          {activeTab === 'customers' && (
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">تقرير العملاء</h2>
              
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم العميل</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">عدد الفواتير</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجمالي المبيعات</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المدفوع</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المتبقي</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">آخر فاتورة</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {customersData.map((customer, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{customer.customerName}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{customer.totalInvoices}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{customer.totalAmount.toLocaleString('ar-LY')} د.ل</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">{customer.paidAmount.toLocaleString('ar-LY')} د.ل</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">{customer.remainingAmount.toLocaleString('ar-LY')} د.ل</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{new Date(customer.lastInvoiceDate).toLocaleDateString('ar-LY')}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Services Report */}
          {activeTab === 'services' && (
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">تقرير الخدمات</h2>
              
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم الخدمة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية المباعة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجمالي المبيعات</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">متوسط السعر</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">عدد مرات الاستخدام</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {servicesData.map((service, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{service.serviceName}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{service.totalQuantity}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{service.totalAmount.toLocaleString('ar-LY')} د.ل</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{service.averagePrice.toLocaleString('ar-LY')} د.ل</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{service.usageCount}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 