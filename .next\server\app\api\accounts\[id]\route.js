(()=>{var e={};e.id=832,e.ids=[832],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52640:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>w,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>h});var n={};r.r(n),r.d(n,{DELETE:()=>p,GET:()=>u,PUT:()=>d});var s=r(96559),o=r(48088),a=r(37719),i=r(32190),c=r(94747);async function u(e,{params:t}){try{let{id:e}=await t,r=await c.z.account.findUnique({where:{id:e},include:{parent:!0,children:!0,_count:{select:{children:!0,debitEntries:!0,creditEntries:!0}}}});if(!r)return i.NextResponse.json({error:"Account not found"},{status:404});return i.NextResponse.json(r)}catch(e){return console.error("Error fetching account:",e),i.NextResponse.json({error:"Failed to fetch account"},{status:500})}}async function d(e,{params:t}){try{let{id:r}=await t,{code:n,name:s,nameEn:o,type:a,parentId:u,isActive:d}=await e.json(),p=await c.z.account.findUnique({where:{id:r}});if(!p)return i.NextResponse.json({error:"Account not found"},{status:404});if(n&&n!==p.code&&await c.z.account.findUnique({where:{code:n}}))return i.NextResponse.json({error:"Account code already exists"},{status:400});let l=await c.z.account.update({where:{id:r},data:{...n&&{code:n},...s&&{name:s},...void 0!==o&&{nameEn:o},...a&&{type:a},...void 0!==u&&{parentId:u},...void 0!==d&&{isActive:d}},include:{parent:!0,children:!0}});return i.NextResponse.json(l)}catch(e){return console.error("Error updating account:",e),i.NextResponse.json({error:"Failed to update account"},{status:500})}}async function p(e,{params:t}){try{let{id:e}=await t,r=await c.z.account.findUnique({where:{id:e},include:{children:!0,_count:{select:{debitEntries:!0,creditEntries:!0}}}});if(!r)return i.NextResponse.json({error:"Account not found"},{status:404});if(r.children.length>0)return i.NextResponse.json({error:"Cannot delete account with sub-accounts"},{status:400});if(r._count.debitEntries>0||r._count.creditEntries>0)return i.NextResponse.json({error:"Cannot delete account with transactions"},{status:400});return await c.z.account.delete({where:{id:e}}),i.NextResponse.json({message:"Account deleted successfully"})}catch(e){return console.error("Error deleting account:",e),i.NextResponse.json({error:"Failed to delete account"},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/accounts/[id]/route",pathname:"/api/accounts/[id]",filename:"route",bundlePath:"app/api/accounts/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\api\\accounts\\[id]\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:x,workUnitAsyncStorage:h,serverHooks:w}=l;function f(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:h})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},94747:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});let n=require("@prisma/client"),s=globalThis.prisma??new n.PrismaClient},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,580],()=>r(52640));module.exports=n})();