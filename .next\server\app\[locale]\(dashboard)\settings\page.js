(()=>{var e={};e.id=639,e.ids=[639],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6405:(e,s,t)=>{Promise.resolve().then(t.bind(t,42606))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42606:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var a=t(60687),r=t(43210),n=t(33213),l=t(62688);let i=(0,l.A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),o=(0,l.A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var d=t(11437);let c=(0,l.A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);function m(){let e=(0,n.useTranslations)(),[s,t]=(0,r.useState)(null),[l,m]=(0,r.useState)(!0),[p,h]=(0,r.useState)(!1),[x,u]=(0,r.useState)("company"),g=async()=>{if(s)try{h(!0),(await fetch("/api/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)})).ok?alert("تم حفظ الإعدادات بنجاح"):alert("حدث خطأ أثناء حفظ الإعدادات")}catch(e){console.error("Error saving settings:",e),alert("حدث خطأ أثناء حفظ الإعدادات")}finally{h(!1)}};return l?(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)("div",{className:"spinner"}),(0,a.jsx)("span",{className:"ml-2",children:e("common.loading")})]}):s?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e("settings.title")}),(0,a.jsxs)("button",{onClick:g,disabled:p,className:"btn-primary flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)(i,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:p?"جاري الحفظ...":e("common.save")})]})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsxs)("nav",{className:"-mb-px flex space-x-8 rtl:space-x-reverse",children:[(0,a.jsxs)("button",{onClick:()=>u("company"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"company"===x?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,a.jsx)(o,{className:"w-4 h-4 inline-block mr-2"}),e("settings.companyInfo")]}),(0,a.jsxs)("button",{onClick:()=>u("localization"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"localization"===x?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,a.jsx)(d.A,{className:"w-4 h-4 inline-block mr-2"}),"اللغة والعملة"]})]})}),"company"===x&&(0,a.jsxs)("div",{className:"card",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-6",children:e("settings.companyInfo")}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:e("settings.companyLogo")}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[s.logo&&(0,a.jsx)("img",{src:s.logo,alt:"Company Logo",className:"w-16 h-16 object-contain border border-gray-300 rounded"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let a=e.target.files?.[0];if(a){let e=new FileReader;e.onload=e=>{s&&t({...s,logo:e.target?.result})},e.readAsDataURL(a)}},className:"hidden",id:"logo-upload"}),(0,a.jsxs)("label",{htmlFor:"logo-upload",className:"btn-secondary cursor-pointer flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)(c,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"رفع شعار"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[e("settings.companyName")," (عربي)"]}),(0,a.jsx)("input",{type:"text",value:s.name,onChange:e=>t({...s,name:e.target.value}),className:"input-field"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[e("settings.companyName")," (English)"]}),(0,a.jsx)("input",{type:"text",value:s.nameEn,onChange:e=>t({...s,nameEn:e.target.value}),className:"input-field"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[e("settings.companyAddress")," (عربي)"]}),(0,a.jsx)("textarea",{value:s.address,onChange:e=>t({...s,address:e.target.value}),className:"input-field",rows:3})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[e("settings.companyAddress")," (English)"]}),(0,a.jsx)("textarea",{value:s.addressEn,onChange:e=>t({...s,addressEn:e.target.value}),className:"input-field",rows:3})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:e("settings.companyPhone")}),(0,a.jsx)("input",{type:"tel",value:s.phone,onChange:e=>t({...s,phone:e.target.value}),className:"input-field"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:e("settings.companyEmail")}),(0,a.jsx)("input",{type:"email",value:s.email,onChange:e=>t({...s,email:e.target.value}),className:"input-field"})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"الموقع الإلكتروني"}),(0,a.jsx)("input",{type:"url",value:s.website,onChange:e=>t({...s,website:e.target.value}),className:"input-field",placeholder:"https://example.com"})]})]})]}),"localization"===x&&(0,a.jsxs)("div",{className:"card",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-6",children:"إعدادات اللغة والعملة"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:e("settings.language")}),(0,a.jsxs)("select",{value:s.language,onChange:e=>t({...s,language:e.target.value}),className:"input-field",children:[(0,a.jsx)("option",{value:"ar",children:"العربية"}),(0,a.jsx)("option",{value:"en",children:"English"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:e("settings.currency")}),(0,a.jsxs)("select",{value:s.currency,onChange:e=>t({...s,currency:e.target.value}),className:"input-field",children:[(0,a.jsx)("option",{value:"LYD",children:e("settings.currencies.LYD")}),(0,a.jsx)("option",{value:"EGP",children:e("settings.currencies.EGP")}),(0,a.jsx)("option",{value:"SAR",children:e("settings.currencies.SAR")})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"ملاحظة:"}),(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"تغيير اللغة سيؤثر على واجهة النظام، بينما تغيير العملة سيؤثر على عرض المبالغ في التقارير والفواتير."})]})]})]}):(0,a.jsx)("div",{children:"خطأ في تحميل الإعدادات"})}},45797:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=t(65239),r=t(48088),n=t(88170),l=t.n(n),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let d={children:["",{children:["[locale]",{children:["(dashboard)",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,66889)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\(dashboard)\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,55338)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\(dashboard)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,11434)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,66196)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\error.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\(dashboard)\\settings\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[locale]/(dashboard)/settings/page",pathname:"/[locale]/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66889:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\SMART\\\\src\\\\app\\\\[locale]\\\\(dashboard)\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\SMART\\src\\app\\[locale]\\(dashboard)\\settings\\page.tsx","default")},96133:(e,s,t)=>{Promise.resolve().then(t.bind(t,66889))}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,756,207,87],()=>t(45797));module.exports=a})();